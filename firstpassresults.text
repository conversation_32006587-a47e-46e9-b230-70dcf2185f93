Starting audit...
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Expected type names/tags: [main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:10:29: Named Constant SOME_RADIUS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:11:32: Named Constant SOME_ANGLE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:12:29: Named Constant START_X defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:13:29: Named Constant START_Y defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:14:29: Named Constant DELTA defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:15:29: Named Constant COUNT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:16:30: Named Constant SLEEP_MS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:32:29: Final parameter args defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:17:1: Class Data Abstraction Coupling is 8 (max allowed is 7) classes [AScalableRectangle, ArthurHead, AvatarImpl, GalahadHead, Gorge, GuardHead, LancelotHead, RobinHead]. [ClassDataAbstractionCoupling]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21:29: Named Constant SOME_X_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22:29: Named Constant SOME_Y_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:23:29: Named Constant LANCELOT_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:24:29: Named Constant ROBIN_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:25:29: Named Constant GALAHAD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:26:29: Named Constant GUARD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:32:30: Named Constant AREA_X_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:33:30: Named Constant KNIGHT_Y_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:34:30: Named Constant GUARD_Y_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:35:30: Named Constant AREA_WIDTH defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:36:30: Named Constant AREA_HEIGHT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:38:30: Named Constant GORGE_X_COORDINATE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:77:26: Final parameter avatar defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:83:21: Final parameter speechText defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:120:24: Final parameter limb defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:120:43: Final parameter dx defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:120:57: Final parameter dy defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:16:21: Variable 'scene' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\ConsoleSceneViewImpl.java:40:32: Final parameter evt defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:7:35: Named Constant MAX_PRINTED_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:8:43: Named Constant MAX_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:9:43: Named Constant PROCESS_TIMEOUT_S defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:10:41: Parameter args should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:28:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:30:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:42:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:43:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:45:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:57:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:58:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:60:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:72:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:73:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:75:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:87:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:88:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:90:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:102:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:109:46: Final parameter listener defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:7:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18:23: Final parameter h defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:22: Final parameter dx defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:36: Final parameter dy defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:54:24: Final parameter limb defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:54:43: Final parameter dx defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:54:57: Final parameter dy defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:55:34: String literal expressions should be on the left side of an equalsIgnoreCase comparison. [EqualsAvoidNull]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:58:41: String literal expressions should be on the left side of an equalsIgnoreCase comparison. [EqualsAvoidNull]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:61:41: String literal expressions should be on the left side of an equalsIgnoreCase comparison. [EqualsAvoidNull]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:64:41: String literal expressions should be on the left side of an equalsIgnoreCase comparison. [EqualsAvoidNull]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:70:23: Final parameter factor defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:9:1: Redundant import from the same package - mp.bridge.Scrollable. [RedundantImport]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:28:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:28:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:29:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:31:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:44:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:46:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:59:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:61:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:73:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:73:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:74:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:76:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:88:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:88:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:89:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:91:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:110:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:28:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:28:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:31:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:44:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:46:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:59:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:61:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:73:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:73:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:74:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:76:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:88:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:88:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:89:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:91:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:110:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:28:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:28:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:29:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:31:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:43:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:44:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:46:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:58:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:59:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:61:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:73:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:73:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:74:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:76:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:88:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:88:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:89:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:91:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:103:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:110:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:27:34: Final parameter fileName defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:27:47: 'fileName' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:28:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:42:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:43:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:45:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:57:22: Final parameter value defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:58:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:60:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:72:26: Final parameter width defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:72:36: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:73:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:75:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:87:27: Final parameter height defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:87:37: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:88:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:90:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:102:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:109:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:28:25: Final parameter t defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:29:16: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:31:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:43:22: Final parameter x defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:44:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:46:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:58:22: Final parameter y defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:59:13: Variable 'oldValue' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:61:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:73:43: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:80:46: Final parameter listener defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:32:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\VShape.java:32:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12:28: Parameter theRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:12:46: Parameter theAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:16:28: Parameter theX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:16:38: Parameter theY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:29:26: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:32:26: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:10:35: Parameter a should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:10:42: Parameter b should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:10:49: Parameter w should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:10:56: Parameter h should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:25:31: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:25:35: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:27:30: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:27:34: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:29:27: Parameter percentage should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:34:26: Parameter a should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScalableRectangle.java:38:26: Parameter b should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:3:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:4:8: Unused import - util.annotations.StructurePattern. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:5:8: Unused import - util.annotations.StructurePatternNames. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRect.java:6:8: Unused import - util.annotations.Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:6:8: Unused import - util.annotations.Tags. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:20:18: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:13:33: Named Constant UNIT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:26:22: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:36:22: Final parameter y defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:53:27: Final parameter radius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63:26: Final parameter angle defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:68:24: Final parameter units defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:73:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:73:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:83:30: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:91:30: Final parameter y defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:99:35: Final parameter radius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:107:34: Final parameter angle defined. Good! [FinalParameterDefined]
Audit done.

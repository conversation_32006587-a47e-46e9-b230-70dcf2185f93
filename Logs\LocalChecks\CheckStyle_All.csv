-1,<PERSON><PERSON> Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,minimumVowelInNameCheck,fn,fn,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.Robin<PERSON><PERSON>,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingSignature,move:int;int->void,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingSignature,scale:double->void//EC,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,image
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,shape
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,c1,c
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumLettersInNameCheck,c1,c,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumVowelInNameCheck,c1,c,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,c2,c
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumLettersInNameCheck,c2,c,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumVowelInNameCheck,c2,c,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,mp.shapes.Gorge,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,mp.shapes.Gorge,gorge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,RIGHT_LINE_X,right
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,RIGHT_LINE_X,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,RIGHT_LINE_X,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_TOP_Y,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_TOP_Y,top
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_TOP_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_HEIGHT,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_HEIGHT,height
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,upper,upper
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,lower,lower
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,leftLine,left
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,leftLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rightLine,right
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rightLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rectangle,rectangle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingGetter,Width,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingGetter,Height,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingSuperType,@Comp301Tags.LOCATABLE,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingInstantiation,java.beans.PropertyChangeEvent,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE],no method
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,publicMethodsDoNotOverride,[public  getLeftLine:->mp.shapes.RotatingLine, public  getRightLine:->mp.shapes.RotatingLine, public  getRectangle:->mp.shapes.AScalableRectangle]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingSetter,Width,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingSetter,Height,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,minimumVowelInNameCheck,dx,dx,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,minimumVowelInNameCheck,dy,dy,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameInDictionary,mp.bridge.Angle,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameInDictionary,mp.bridge.Angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameNotInDictionary,r,r
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,minimumLettersInNameCheck,r,r,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,minimumVowelInNameCheck,r,r,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,rotate
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,units,units
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,left
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,right
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getLeftLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getRightLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingSignature,move:int;int->void,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingSignature,scale:double->void//EC,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,galahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingSignature,move:int;int->void,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingSignature,scale:double->void//EC,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,lancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getX:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setX:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getY:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setY:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingSignature,move:int;int->void,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingSignature,scale:double->void//EC,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,b,b
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,minimumLettersInNameCheck,b,b,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,minimumVowelInNameCheck,b,b,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,r,r
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,rotating
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,point,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,a,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,UNIT,unit
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,units,units
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Radius,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Angle,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSignature,rotate:int->void,mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSignature,move:int;int->void,mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,leftLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,rightLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,getLeftLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,getRightLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Radius,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Angle,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,polar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,radius,radius
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getAngle:->double
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getRadius:->double
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.LancelotHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.LancelotHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,mp.bridge.BridgeScene,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,mp.bridge.BridgeScene,scene
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameInDictionary,mp.bridge.Avatar,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameInDictionary,mp.bridge.Avatar,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,ImageShape,getHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Angle,getArms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Angle,getLegs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getArthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getLancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getRobin
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGalahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGuard
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameNotInDictionary,t,t
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,minimumLettersInNameCheck,t,t,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,minimumVowelInNameCheck,t,t,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,speech
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,bubble
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,text,text
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,missingSuperType,@Comp301Tags.LOCATABLE,mp.bridge.SpeechBubble
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,arthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingSignature,move:int;int->void,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingSignature,scale:double->void//EC,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameNotInDictionary,mp.shapes.AScalableRectangle,scalable
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,rectangle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,width,width
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,height,height
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,newVal,new
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,newVal,val
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,percentage,percentage
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,liberalMagicNumber,100,100
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.ArthurHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.ArthurHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Height:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Width:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.ArthurHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.ArthurHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,publicMethodsDoNotOverride,[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  setHeight:int->void, public  setWidth:int->void, public  scale:int->void]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,lance_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,robin_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,gal_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,guard_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,s,s
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,minimumLettersInNameCheck,s,s,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,minimumVowelInNameCheck,s,s,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,main
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,scene
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,impl
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,arthur,arthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,lancelot,lancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,robin,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,galahad,galahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guard,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_x,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_y,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,lance_const,lance
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,robin_const,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,gal_const,gal
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guard_const,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,gorge,gorge
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,cur,cur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightArea,knight
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightArea,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guardArea,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guardArea,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KnightTurn,knight
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KnightTurn,turn
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_X,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_X,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KNIGHT_Y,knight
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KNIGHT_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_Y,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_WIDTH,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_WIDTH,width
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_HEIGHT,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_HEIGHT,height
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,Occupied,occupied
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,avatar,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,arthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,lancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,galahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,liberalMagicNumber,750,EXPR 750
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,bulkierThen,60.0,5.0,12.0
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,thenBranching,say
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nestedIfDepth,2,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getArthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getLancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getRobin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getGalahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getGuard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,publicMethodsDoNotOverride,[public  approach:mp.bridge.AvatarImpl->void, public  say:String->void, public  getKnightArea:->mp.shapes.AScalableRectangle, public  getGuardArea:->mp.shapes.AScalableRectangle, public  getGorge:->mp.shapes.Gorge]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedInstantiation,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE],[public  BridgeSceneImpl:->]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Arthur,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Galahad,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Lancelot,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Robin,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Guard,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,impl
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,head,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,speech,speech
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,arms,arms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,legs,legs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,arms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,legs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,getHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,getArms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,getLegs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,approach:@Comp301Tags.AVATAR->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,say:String->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,passed:->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,failed:->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,@scroll:int;int->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedSignature,move:int;int->void,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedInstantiation,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],[public  AvatarImpl:mp.bridge.ImageShape->]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,scale:double->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasClassType,AvatarImpl,cur
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasClassType,AvatarImpl,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Point.java,nameInDictionary,mp.shapes.Point,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Point.java,nameInDictionary,mp.shapes.Point,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Point.java,variableHasInterfaceType,Point,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameNotInDictionary,t,t
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,string
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,shape
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,variableHasInterfaceType,StringShape,getStringShape
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,variableHasInterfaceType,StringShape,speech
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,main
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,run
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,tests
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,args,args
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,liberalMagicNumber,600,EXPR 600
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,liberalMagicNumber,2000,EXPR 2000
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,liberalMagicNumber,5,EXPR 5
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameNotInDictionary,SOME_RAD,rad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameNotInDictionary,D,d
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,minimumLettersInNameCheck,D,d,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,minimumVowelInNameCheck,D,d,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,main.Assignment1,main
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,main.Assignment1,assignment
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SOME_RAD,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SOME_ANGLE,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SOME_ANGLE,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_X,start
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_X,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_Y,start
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,COUNT,count
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SLEEP_MS,sleep
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SLEEP_MS,ms
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,line,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,frame,frame
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,i,i
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,args,args
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,variableHasInterfaceType,RotateLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,expectedInstantiation,@Comp301Tags.BRIDGE_SCENE,main.Assignment1[main.Assignment1],[static public  main:String[]->void]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,nameNotInDictionary,dx,dx
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,minimumVowelInNameCheck,dx,dx,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,nameNotInDictionary,dy,dy
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,minimumVowelInNameCheck,dy,dy,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,nameInDictionary,mp.bridge.Avatar,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,nameInDictionary,mp.bridge.Avatar,avatar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,missingSignature,@scroll:int;int->void//EC,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,arthur
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,lancelot
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,robin
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,galahad
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,guard
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,cur
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,avatar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getArthur
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getLancelot
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getRobin
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGalahad
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGuard
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,expectedSignature,approach:@Comp301Tags.AVATAR->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,expectedSignature,say:String->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,expectedSignature,passed:->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Avatar.java,expectedSignature,failed:->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Angle.java,nameInDictionary,mp.bridge.Angle,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Angle.java,nameInDictionary,mp.bridge.Angle,angle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Angle.java,variableHasInterfaceType,Angle,getArms
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\Angle.java,variableHasInterfaceType,Angle,getLegs
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,galahad
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,file,file
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameNotInDictionary,fn,fn
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,minimumVowelInNameCheck,fn,fn,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,nameNotInDictionary,n,n
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,minimumLettersInNameCheck,n,n,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,minimumVowelInNameCheck,n,n,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,missingSignature,move:int;int->void,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GalahadHead.java,missingSignature,scale:double->void//EC,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,arthur
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,file,file
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameNotInDictionary,fn,fn
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,nameNotInDictionary,n,n
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getImageFileName:->String
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setImageFileName:String->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,missingSignature,move:int;int->void,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ArthurHead.java,missingSignature,scale:double->void//EC,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,mp.shapes.Gorge,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,mp.shapes.Gorge,gorge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,upper,upper
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,lower,lower
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,leftLine,left
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,leftLine,line
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rightLine,right
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rightLine,line
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rectangle,rectangle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,rightlinex,rightlinex
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,linetopy,linetopy
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,lineheight,lineheight
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,c1,c
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,minimumLettersInNameCheck,c1,c,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,minimumVowelInNameCheck,c1,c,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,c2,c
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,minimumLettersInNameCheck,c2,c,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,minimumVowelInNameCheck,c2,c,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,variableHasClassType,Gorge,gorge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,variableHasClassType,Gorge,getGorge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Gorge.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,rotating
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,line
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,point,point
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,a,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,UNIT,unit
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,angle,angle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,units,units
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,b,b
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,minimumLettersInNameCheck,b,b,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,minimumVowelInNameCheck,b,b,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,r,r
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,minimumLettersInNameCheck,r,r,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,minimumVowelInNameCheck,r,r,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,dx,dx
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,dy,dy
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingSetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingSetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingInstantiation,java.beans.PropertyChangeEvent,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE],no method
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingGetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingGetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingGetter,PropertyChangeListeners,List,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,missingInterface,util.models.PropertyListenerRegisterer,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotatingLine.java,classDoesNotHaveExactlyOneInterface,mp.shapes.RotatingLine,No Tag
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\StaticFactoryClass.java,nameInDictionary,main.StaticFactoryClass,main
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\StaticFactoryClass.java,nameInDictionary,main.StaticFactoryClass,static
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\StaticFactoryClass.java,nameInDictionary,main.StaticFactoryClass,factory
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\StaticFactoryClass.java,nameInDictionary,main.StaticFactoryClass,class
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\StaticFactoryClass.java,nameInDictionary,scene,scene
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\StaticFactoryClass.java,variableHasInterfaceType,Angle,legsFactoryMethod
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,guard
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,file,file
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameNotInDictionary,fn,fn
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,nameNotInDictionary,n,n
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,missingSignature,move:int;int->void,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\GuardHead.java,missingSignature,scale:double->void//EC,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,polar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,point
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,radius,radius
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,angle,angle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,setterAssignsGlobal,setX
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,setterAssignsGlobal,setY
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\APolarPoint.java,classDoesNotHaveExactlyOneInterface,mp.shapes.APolarPoint,No Tag
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,nameNotInDictionary,t,t
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,minimumLettersInNameCheck,t,t,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,minimumVowelInNameCheck,t,t,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,string
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,shape
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\StringShape.java,variableHasInterfaceType,StringShape,getStringShape
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\PolarPointInterface.java,nameInDictionary,mp.shapes.PolarPointInterface,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\PolarPointInterface.java,nameInDictionary,mp.shapes.PolarPointInterface,polar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\PolarPointInterface.java,nameInDictionary,mp.shapes.PolarPointInterface,point
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\PolarPointInterface.java,nameInDictionary,mp.shapes.PolarPointInterface,interface
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,avatar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,impl
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,head,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,speech,speech
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,arms,arms
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,legs,legs
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameNotInDictionary,dx,dx
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,nameNotInDictionary,dy,dy
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,StringShape,speech
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,arms
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,legs
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,StringShape,getStringShape
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,getArms
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,getLegs
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedSignature,move:int;int->void,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,classDoesNotHaveExactlyOneInterface,mp.bridge.AvatarImpl,No Tag
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,scale:double->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameNotInDictionary,mp.shapes.AScalableRectangleInterface,scalable
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameInDictionary,mp.shapes.AScalableRectangleInterface,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameInDictionary,mp.shapes.AScalableRectangleInterface,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameInDictionary,mp.shapes.AScalableRectangleInterface,rectangle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameInDictionary,mp.shapes.AScalableRectangleInterface,interface
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameInDictionary,percentage,percentage
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,knightArea
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,guardArea
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,getKnightArea
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,getGuardArea
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,rectangle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,getRectangle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\GetRect.java,nameNotInDictionary,mp.shapes.GetRect,rect
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\GetRect.java,nameInDictionary,mp.shapes.GetRect,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\GetRect.java,nameInDictionary,mp.shapes.GetRect,get
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\GetRect.java,variableHasInterfaceType,AScalableRectangleInterface,getRectangle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,nameNotInDictionary,fn,fn
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,image
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,shape
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,variableHasInterfaceType,ImageShape,getHead
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,variableHasInterfaceType,ImageShape,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\ImageShape.java,variableHasInterfaceType,ImageShape,h
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Get.java,nameInDictionary,mp.shapes.Get,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Get.java,nameInDictionary,mp.shapes.Get,get
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,rectangle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,width,width
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,height,height
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,percentConversion,percent
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,percentConversion,conversion
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,percentage,percentage
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,a,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameNotInDictionary,mp.shapes.AScalableRectangle,scalable
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameNotInDictionary,b,b
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingSetter,Width,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingSetter,Height,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingSuperType,@Comp301Tags.LOCATABLE,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingGetter,Width,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingGetter,Height,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,classDoesNotHaveExactlyOneInterface,mp.shapes.AScalableRectangle,No Tag
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingInstantiation,java.beans.PropertyChangeEvent,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE],no method
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Locatable.java,nameInDictionary,mp.shapes.Locatable,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Locatable.java,nameInDictionary,mp.shapes.Locatable,locatable
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Locatable.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Locatable.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Point.java,nameInDictionary,mp.shapes.Point,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Point.java,nameInDictionary,mp.shapes.Point,point
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Point.java,variableHasInterfaceType,Point,point
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\BoundedShape.java,nameInDictionary,mp.shapes.BoundedShape,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\BoundedShape.java,nameInDictionary,mp.shapes.BoundedShape,bounded
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\BoundedShape.java,nameInDictionary,mp.shapes.BoundedShape,shape
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameInDictionary,mp.bridge.VShape,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameInDictionary,mp.bridge.VShape,shape
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameInDictionary,left,left
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameInDictionary,right,right
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameNotInDictionary,mp.bridge.VShape,v
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,minimumLettersInNameCheck,mp.bridge.VShape,v,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,minimumVowelInNameCheck,mp.bridge.VShape,v,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameNotInDictionary,dx,dx
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,nameNotInDictionary,dy,dy
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,expectedSignature,move:int;int->void,mp.bridge.VShape:[@Comp301Tags.ANGLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,peerDuplicatedSignatures,mp.shapes.Gorge,public  getLeftLine:->mp.shapes.RotateLine
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,peerDuplicatedSignatures,mp.shapes.Gorge,public  getRightLine:->mp.shapes.RotateLine
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,missingGetter,LeftLine,@Comp301Tags.ROTATING_LINE,mp.bridge.VShape[@Comp301Tags.ANGLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,missingGetter,RightLine,@Comp301Tags.ROTATING_LINE,mp.bridge.VShape[@Comp301Tags.ANGLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\VShape.java,classDoesNotHaveExactlyOneInterface,mp.bridge.VShape,No Tag
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Moveable.java,nameNotInDictionary,dx,dx
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Moveable.java,nameNotInDictionary,dy,dy
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Moveable.java,nameInDictionary,mp.shapes.Moveable,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\Moveable.java,nameInDictionary,mp.shapes.Moveable,moveable
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,nameNotInDictionary,s,s
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,minimumLettersInNameCheck,s,s,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,minimumVowelInNameCheck,s,s,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,mp.bridge.BridgeScene,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,mp.bridge.BridgeScene,scene
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,avatar,avatar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,Avatar,getArthur
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,Avatar,getLancelot
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,Avatar,getRobin
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,Avatar,getGalahad
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,Avatar,getGuard
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,Avatar,avatar
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,AScalableRectangleInterface,getKnightArea
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,AScalableRectangleInterface,getGuardArea
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasClassType,Gorge,getGorge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,BridgeScene,scene
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\BridgeScene.java,variableHasInterfaceType,BridgeScene,bridgeSceneFactoryMethod
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,lancelot
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,file,file
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,a,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameNotInDictionary,fn,fn
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,nameNotInDictionary,b,b
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,missingSignature,move:int;int->void,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\LancelotHead.java,missingSignature,scale:double->void//EC,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,nameNotInDictionary,r,r
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,shapes
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,rotate
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,line
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,angle,angle
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,units,units
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,left
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,right
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getLeftLine
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getRightLine
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,leftLine
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,rightLine
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,main
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,run
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,tests
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,MAX_PRINTED_TRACES,max
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,MAX_PRINTED_TRACES,printed
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,MAX_PRINTED_TRACES,traces
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,MAX_TRACES,max
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,MAX_TRACES,traces
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,PROCESS_TIMEOUT_S,process
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,args,args
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameNotInDictionary,PROCESS_TIMEOUT_S,timeout
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,nameNotInDictionary,PROCESS_TIMEOUT_S,s
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,minimumLettersInNameCheck,PROCESS_TIMEOUT_S,s,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\RunSS25A2Tests.java,minimumVowelInNameCheck,PROCESS_TIMEOUT_S,s,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,speech
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,bubble
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,text,text
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,a,a
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameNotInDictionary,b,b
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,nameNotInDictionary,t,t
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.bridge.LancelotHead
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.bridge.LancelotHead
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,expectedGetter,X,int,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,expectedGetter,Y,int,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,missingInstantiation,java.beans.PropertyChangeEvent,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE],no method
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,missingGetter,PropertyChangeListeners,List,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,missingInterface,util.models.PropertyListenerRegisterer,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getX:->int,[mp.shapes.Locatable]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setX:int->void,[mp.shapes.Locatable]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getY:->int,[mp.shapes.Locatable]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setY:int->void,[mp.shapes.Locatable]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,expectedSetter,X,int,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\SpeechBubble.java,expectedSetter,Y,int,mp.bridge.SpeechBubble[@Comp301Tags.LOCATABLE]
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,main.Assignment2,main
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,main.Assignment2,assignment
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,SOME_RAD,some
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,SOME_ANGLE,some
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,SOME_ANGLE,angle
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,START_X,start
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,START_X,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,START_Y,start
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,START_Y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,COUNT,count
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,SLEEP_MS,sleep
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,SLEEP_MS,ms
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,line,line
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,frame,frame
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,i,i
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,scene,scene
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameInDictionary,args,args
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameNotInDictionary,SOME_RAD,rad
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,nameNotInDictionary,D,d
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,minimumLettersInNameCheck,D,d,2
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,minimumVowelInNameCheck,D,d,1
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,variableHasInterfaceType,RotateLine,line
-1,Wed Jul 09 13:13:10 EDT 2025,true,main\Assignment2.java,variableHasInterfaceType,BridgeScene,scene
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,bridge
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,robin
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,head
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,x,x
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,y,y
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,file,file
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameNotInDictionary,fn,fn
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,nameNotInDictionary,n,n
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),RobinHead,mp.bridge.LancelotHead
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.bridge.LancelotHead
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.bridge.LancelotHead
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.bridge.SpeechBubble
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.bridge.SpeechBubble
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,publicMethodsOverride,[]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getX:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setX:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getY:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setY:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,missingSignature,move:int;int->void,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
-1,Wed Jul 09 13:13:10 EDT 2025,true,mp\bridge\RobinHead.java,missingSignature,scale:double->void//EC,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedTypes,[main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,main
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,bridge
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,scene
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,impl
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,arthur,arthur
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,lancelot,lancelot
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,robin,robin
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,galahad,galahad
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guard,guard
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,SOME_X,some
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,SOME_X,x
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,SOME_Y,some
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,SOME_Y,y
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GAL_CONST,gal
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_CONST,guard
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,gorge,gorge
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,cur,cur
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightArea,knight
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightArea,area
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guardArea,guard
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guardArea,area
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightTurn,knight
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightTurn,turn
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_X,area
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_X,x
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KNIGHT_Y,knight
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KNIGHT_Y,y
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_Y,guard
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_Y,y
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_WIDTH,area
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_WIDTH,width
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_HEIGHT,area
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_HEIGHT,height
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,occupied,occupied
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GORGE_X,gorge
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GORGE_X,x
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,avatar,avatar
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,L_CONST,l
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,minimumLettersInNameCheck,L_CONST,l,2
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,minimumVowelInNameCheck,L_CONST,l,1
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,L_CONST,const
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,R_CONST,r
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,minimumLettersInNameCheck,R_CONST,r,2
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,minimumVowelInNameCheck,R_CONST,r,1
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,R_CONST,const
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,GAL_CONST,const
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,GUARD_CONST,const
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,gorgey,gorgey
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,diff,diff
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,s,s
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,arthur
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,lancelot
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,robin
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,galahad
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,guard
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasClassType,Gorge,gorge
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,cur
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,AScalableRectangleInterface,knightArea
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,AScalableRectangleInterface,guardArea
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,thenBranching,failed
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,avatar
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,bulkierThen,60.0,5.0,12.0
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,thenBranching,say
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getArthur
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getLancelot
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getRobin
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getGalahad
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getGuard
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,AScalableRectangleInterface,getKnightArea
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,AScalableRectangleInterface,getGuardArea
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,variableHasClassType,Gorge,getGorge
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,missingSignature,@scroll:int;int->void//EC,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedInstantiation,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE],[public  BridgeSceneImpl:->]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Arthur,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Galahad,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Lancelot,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Robin,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Guard,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedSignature,approach:@Comp301Tags.AVATAR->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedSignature,say:String->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedSignature,passed:->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,expectedSignature,failed:->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\BridgeSceneImpl.java,publicMethodsDoNotOverride,[public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,ImageShape,getHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,StringShape,getStringShape
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Angle,getArms
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Angle,getLegs
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\Angle.java,variableHasInterfaceType,RotateLine,getLeftLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\Angle.java,variableHasInterfaceType,RotateLine,getRightLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getX:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setX:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getY:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setY:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.SpeechBubble,public  getX:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.SpeechBubble,public  setX:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.SpeechBubble,public  getY:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.SpeechBubble,public  setY:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,variableHasInterfaceType,RotateLine,leftLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,variableHasInterfaceType,RotateLine,rightLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,variableHasInterfaceType,AScalableRectangleInterface,rectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,variableHasInterfaceType,RotateLine,getLeftLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,variableHasInterfaceType,RotateLine,getRightLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,variableHasInterfaceType,AScalableRectangleInterface,getRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,peerDuplicatedSignatures,mp.bridge.VShape,public  getLeftLine:->mp.shapes.RotateLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Gorge.java,peerDuplicatedSignatures,mp.bridge.VShape,public  getRightLine:->mp.shapes.RotateLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasInterfaceType,Point,point
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.AvatarImpl,public  move:int;int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.VShape,public  move:int;int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getWidth:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getHeight:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getRadius:->double
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getAngle:->double
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getX:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setX:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getY:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setY:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getX:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setX:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getY:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setY:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getX:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setX:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getY:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setY:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
1,Wed Jul 09 13:18:27 EDT 2025,true,main\StaticFactoryClass.java,variableHasInterfaceType,BridgeScene,scene
1,Wed Jul 09 13:18:27 EDT 2025,true,main\StaticFactoryClass.java,variableHasInterfaceType,BridgeScene,bridgeSceneFactoryMethod
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GuardHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GuardHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GuardHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GuardHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getX:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setX:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getY:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setY:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Radius:double(public , null),APolarPoint,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Angle:double(public , null),APolarPoint,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,head
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,h
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,getHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,peerDuplicatedSignatures,mp.bridge.VShape,public  move:int;int->void
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedGetter,StringShape,@STRING_PATTERN,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedGetter,Head,@IMAGE_PATTERN,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Get.java,variableHasInterfaceType,RotateLine,getLeftLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\Get.java,variableHasInterfaceType,RotateLine,getRightLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Height:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Width:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.bridge.LancelotHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public X:int(public ,public ),AScalableRectangle,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Y:int(public ,public ),AScalableRectangle,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,expectedSetter,Width,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,expectedSetter,Height,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.APolarPoint,public  getX:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.APolarPoint,public  getY:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.APolarPoint,public  setX:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.APolarPoint,public  setY:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,expectedGetter,Width,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\shapes\AScalableRectangle.java,expectedGetter,Height,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,variableHasInterfaceType,RotateLine,left
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,variableHasInterfaceType,RotateLine,right
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,variableHasInterfaceType,RotateLine,getLeftLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,variableHasInterfaceType,RotateLine,getRightLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,peerDoesNotHaveCommonProperties,readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null),VShape,mp.shapes.Gorge
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,peerDoesNotHaveCommonProperties,readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null),VShape,mp.shapes.Gorge
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,peerOverriddingSignatures,mp.shapes.Gorge,public  getLeftLine:->mp.shapes.RotateLine,[mp.shapes.Get]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\VShape.java,peerOverriddingSignatures,mp.shapes.Gorge,public  getRightLine:->mp.shapes.RotateLine,[mp.shapes.Get]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.shapes.AScalableRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.shapes.AScalableRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.SpeechBubble
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  getX:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  setX:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  getY:->int,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  setY:int->void,[mp.shapes.Locatable]
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.bridge.RobinHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.shapes.AScalableRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.shapes.AScalableRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),SpeechBubble,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),SpeechBubble,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),RobinHead,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.bridge.GuardHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),RobinHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.bridge.GalahadHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.shapes.RotatingLine
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.shapes.AScalableRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.shapes.AScalableRectangle
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),RobinHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.bridge.ArthurHead
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RobinHead,mp.shapes.APolarPoint
1,Wed Jul 09 13:18:27 EDT 2025,true,mp\bridge\RobinHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RobinHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:32:04 EDT 2025,false,guardArea,nameInDictionary,guardArea,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,guardArea,nameInDictionary,guardArea,area
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.Angle,nameInDictionary,mp.bridge.Angle,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.Angle,nameInDictionary,mp.bridge.Angle,angle
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,nameInDictionary,mp.bridge.GalahadHead,galahad
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setX:int->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setY:int->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getY:->int,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,nameInDictionary,mp.bridge.GalahadHead,head
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getX:->int,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,nameInDictionary,mp.bridge.GalahadHead,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GalahadHead,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,@Comp301Tags.AVATAR,expectedInstantiation,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE],[public  BridgeSceneImpl:->]
2,Wed Jul 09 13:32:04 EDT 2025,false,legs,nameInDictionary,legs,legs
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotateLine,nameInDictionary,mp.shapes.RotateLine,rotate
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotateLine,nameInDictionary,mp.shapes.RotateLine,shapes
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotateLine,nameInDictionary,mp.shapes.RotateLine,line
2,Wed Jul 09 13:32:04 EDT 2025,false,newVal,nameInDictionary,newVal,new
2,Wed Jul 09 13:32:04 EDT 2025,false,newVal,nameInDictionary,newVal,val
2,Wed Jul 09 13:32:04 EDT 2025,false,galahad,nameInDictionary,galahad,galahad
2,Wed Jul 09 13:32:04 EDT 2025,false,SLEEP_MS,nameInDictionary,SLEEP_MS,ms
2,Wed Jul 09 13:32:04 EDT 2025,false,SLEEP_MS,nameInDictionary,SLEEP_MS,sleep
2,Wed Jul 09 13:32:04 EDT 2025,false,c1,minimumVowelInNameCheck,c1,c,1
2,Wed Jul 09 13:32:04 EDT 2025,false,c1,nameNotInDictionary,c1,c
2,Wed Jul 09 13:32:04 EDT 2025,false,c1,minimumLettersInNameCheck,c1,c,2
2,Wed Jul 09 13:32:04 EDT 2025,false,c2,nameNotInDictionary,c2,c
2,Wed Jul 09 13:32:04 EDT 2025,false,c2,minimumVowelInNameCheck,c2,c,1
2,Wed Jul 09 13:32:04 EDT 2025,false,c2,minimumLettersInNameCheck,c2,c,2
2,Wed Jul 09 13:32:04 EDT 2025,false,2,nestedIfDepth,2,1
2,Wed Jul 09 13:32:04 EDT 2025,false,5,liberalMagicNumber,5,EXPR 5
2,Wed Jul 09 13:32:04 EDT 2025,false,rotate:int->void,expectedSignature,rotate:int->void,mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,D,minimumVowelInNameCheck,D,d,1
2,Wed Jul 09 13:32:04 EDT 2025,false,D,nameNotInDictionary,D,d
2,Wed Jul 09 13:32:04 EDT 2025,false,D,minimumLettersInNameCheck,D,d,2
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,X,expectedSetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,X,expectedGetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Y,expectedGetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Y,expectedSetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Legs,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Legs,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Legs,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Legs,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Legs,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,a,nameInDictionary,a,a
2,Wed Jul 09 13:32:04 EDT 2025,false,b,minimumLettersInNameCheck,b,b,2
2,Wed Jul 09 13:32:04 EDT 2025,false,b,nameNotInDictionary,b,b
2,Wed Jul 09 13:32:04 EDT 2025,false,b,minimumVowelInNameCheck,b,b,1
2,Wed Jul 09 13:32:04 EDT 2025,false,@scroll:int;int->void//EC,missingSignature,@scroll:int;int->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,i,nameInDictionary,i,i
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ImageShape,nameInDictionary,mp.bridge.ImageShape,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ImageShape,nameInDictionary,mp.bridge.ImageShape,image
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ImageShape,nameInDictionary,mp.bridge.ImageShape,shape
2,Wed Jul 09 13:32:04 EDT 2025,false,r,minimumLettersInNameCheck,r,r,2
2,Wed Jul 09 13:32:04 EDT 2025,false,r,nameNotInDictionary,r,r
2,Wed Jul 09 13:32:04 EDT 2025,false,r,minimumVowelInNameCheck,r,r,1
2,Wed Jul 09 13:32:04 EDT 2025,false,s,minimumLettersInNameCheck,s,s,2
2,Wed Jul 09 13:32:04 EDT 2025,false,s,nameNotInDictionary,s,s
2,Wed Jul 09 13:32:04 EDT 2025,false,s,minimumVowelInNameCheck,s,s,1
2,Wed Jul 09 13:32:04 EDT 2025,false,t,nameNotInDictionary,t,t
2,Wed Jul 09 13:32:04 EDT 2025,false,t,minimumLettersInNameCheck,t,t,2
2,Wed Jul 09 13:32:04 EDT 2025,false,t,minimumVowelInNameCheck,t,t,1
2,Wed Jul 09 13:32:04 EDT 2025,false,x,nameInDictionary,x,x
2,Wed Jul 09 13:32:04 EDT 2025,false,y,nameInDictionary,y,y
2,Wed Jul 09 13:32:04 EDT 2025,false,dx,minimumVowelInNameCheck,dx,dx,1
2,Wed Jul 09 13:32:04 EDT 2025,false,dx,nameNotInDictionary,dx,dx
2,Wed Jul 09 13:32:04 EDT 2025,false,dy,nameNotInDictionary,dy,dy
2,Wed Jul 09 13:32:04 EDT 2025,false,dy,minimumVowelInNameCheck,dy,dy,1
2,Wed Jul 09 13:32:04 EDT 2025,false,radius,nameInDictionary,radius,radius
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\BridgeScene.java,variableHasInterfaceType,BridgeScene,bridgeSceneFactoryMethod
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\BridgeScene.java,variableHasInterfaceType,BridgeScene,scene
2,Wed Jul 09 13:32:04 EDT 2025,false,2000,liberalMagicNumber,2000,EXPR 2000
2,Wed Jul 09 13:32:04 EDT 2025,false,knightArea,nameInDictionary,knightArea,knight
2,Wed Jul 09 13:32:04 EDT 2025,false,knightArea,nameInDictionary,knightArea,area
2,Wed Jul 09 13:32:04 EDT 2025,false,600,liberalMagicNumber,600,EXPR 600
2,Wed Jul 09 13:32:04 EDT 2025,false,frame,nameInDictionary,frame,frame
2,Wed Jul 09 13:32:04 EDT 2025,false,fn,minimumVowelInNameCheck,fn,fn,1
2,Wed Jul 09 13:32:04 EDT 2025,false,fn,nameNotInDictionary,fn,fn
2,Wed Jul 09 13:32:04 EDT 2025,false,RotatingLine,variableHasClassType,RotatingLine,getLeftLine
2,Wed Jul 09 13:32:04 EDT 2025,false,RotatingLine,variableHasClassType,RotatingLine,leftLine
2,Wed Jul 09 13:32:04 EDT 2025,false,RotatingLine,variableHasClassType,RotatingLine,getRightLine
2,Wed Jul 09 13:32:04 EDT 2025,false,RotatingLine,variableHasClassType,RotatingLine,rightLine
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Angle.java,variableHasInterfaceType,Angle,getLegs
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Angle.java,variableHasInterfaceType,Angle,getArms
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\Gorge.java,variableHasClassType,Gorge,gorge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\Gorge.java,variableHasClassType,Gorge,getGorge
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,galahad
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,getArthur
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,lancelot
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,getGalahad
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,getGuard
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,robin
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,getRobin
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,getLancelot
2,Wed Jul 09 13:32:04 EDT 2025,false,Avatar,variableHasInterfaceType,Avatar,arthur
2,Wed Jul 09 13:32:04 EDT 2025,false,args,nameInDictionary,args,args
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\Point.java,variableHasInterfaceType,Point,point
2,Wed Jul 09 13:32:04 EDT 2025,false,750,liberalMagicNumber,750,EXPR 750
2,Wed Jul 09 13:32:04 EDT 2025,false,text,nameInDictionary,text,text
2,Wed Jul 09 13:32:04 EDT 2025,false,lower,nameInDictionary,lower,lower
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,galahad
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getArthur
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,expectedSignature,say:String->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,lancelot
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,cur
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGuard
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,avatar
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getRobin
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getLancelot
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,arthur
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,missingSignature,@scroll:int;int->void//EC,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,expectedSignature,failed:->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,expectedSignature,approach:@Comp301Tags.AVATAR->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,expectedSignature,passed:->void,main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGalahad
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,robin
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ArthurHead,nameInDictionary,mp.bridge.ArthurHead,head
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ArthurHead,peerDuplicatedSignatures,mp.bridge.ArthurHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ArthurHead,nameInDictionary,mp.bridge.ArthurHead,arthur
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ArthurHead,peerDuplicatedSignatures,mp.bridge.ArthurHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.ArthurHead,nameInDictionary,mp.bridge.ArthurHead,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,Robin,expectedGetter,Robin,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,variableHasInterfaceType,StringShape,getStringShape
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,StringShape,variableHasInterfaceType,StringShape,speech
2,Wed Jul 09 13:32:04 EDT 2025,false,[public  getX:->int,publicMethodsDoNotOverride,[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  setHeight:int->void, public  setWidth:int->void, public  scale:int->void]
2,Wed Jul 09 13:32:04 EDT 2025,false,angle,nameInDictionary,angle,angle
2,Wed Jul 09 13:32:04 EDT 2025,false,Width,missingGetter,Width,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Width,missingSetter,Width,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,robin_const,nameInDictionary,robin_const,robin
2,Wed Jul 09 13:32:04 EDT 2025,false,robin_const,nameNotInDictionary,robin_const,const
2,Wed Jul 09 13:32:04 EDT 2025,false,some_x,nameInDictionary,some_x,some
2,Wed Jul 09 13:32:04 EDT 2025,false,some_x,nameInDictionary,some_x,x
2,Wed Jul 09 13:32:04 EDT 2025,false,some_y,nameInDictionary,some_y,y
2,Wed Jul 09 13:32:04 EDT 2025,false,some_y,nameInDictionary,some_y,some
2,Wed Jul 09 13:32:04 EDT 2025,false,AREA_HEIGHT,nameInDictionary,AREA_HEIGHT,area
2,Wed Jul 09 13:32:04 EDT 2025,false,AREA_HEIGHT,nameInDictionary,AREA_HEIGHT,height
2,Wed Jul 09 13:32:04 EDT 2025,false,avatar,nameInDictionary,avatar,avatar
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.Gorge,nameInDictionary,mp.shapes.Gorge,shapes
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.Gorge,nameInDictionary,mp.shapes.Gorge,gorge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],missingGetter,Head,@IMAGE_PATTERN,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],missingGetter,StringShape,@STRING_PATTERN,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,UNIT,nameInDictionary,UNIT,unit
2,Wed Jul 09 13:32:04 EDT 2025,false,Occupied,nameInDictionary,Occupied,occupied
2,Wed Jul 09 13:32:04 EDT 2025,false,Arms,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Arms,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Arms,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Arms,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Arms,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE],missingGetter,Width,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE],missingSetter,Height,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE],missingSetter,Width,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE],missingGetter,Height,int,mp.shapes.AScalableRectangle[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,arms,nameInDictionary,arms,arms
2,Wed Jul 09 13:32:04 EDT 2025,false,lance_const,nameInDictionary,lance_const,lance
2,Wed Jul 09 13:32:04 EDT 2025,false,lance_const,nameNotInDictionary,lance_const,const
2,Wed Jul 09 13:32:04 EDT 2025,false,GUARD_Y,nameInDictionary,GUARD_Y,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,GUARD_Y,nameInDictionary,GUARD_Y,y
2,Wed Jul 09 13:32:04 EDT 2025,false,Radius,expectedGetter,Radius,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Radius,expectedSetter,Radius,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,units,nameInDictionary,units,units
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\VShape.java,peerDuplicatedSignatures,mp.shapes.Gorge,public  getRightLine:->mp.shapes.RotateLine
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\VShape.java,peerDuplicatedSignatures,mp.shapes.Gorge,public  getLeftLine:->mp.shapes.RotateLine
2,Wed Jul 09 13:32:04 EDT 2025,false,head,nameInDictionary,head,head
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\RobinHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setImageFileName:String->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getImageFileName:->String
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,Height,missingSetter,Height,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Height,missingGetter,Height,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,nameInDictionary,mp.bridge.RobinHead,robin
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,nameInDictionary,mp.bridge.RobinHead,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.RobinHead,nameInDictionary,mp.bridge.RobinHead,head
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,missingSignature,move:int;int->void,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,missingSignature,move:int;int->void,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,missingSignature,move:int;int->void,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,expectedSignature,move:int;int->void,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,missingSignature,move:int;int->void,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,expectedSignature,move:int;int->void,mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,move:int;int->void,missingSignature,move:int;int->void,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,java.beans.PropertyChangeEvent,missingInstantiation,java.beans.PropertyChangeEvent,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE],no method
2,Wed Jul 09 13:32:04 EDT 2025,false,60.0,bulkierThen,60.0,5.0,12.0
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Width:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Height:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,editable,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
2,Wed Jul 09 13:32:04 EDT 2025,false,Point,variableHasInterfaceType,Point,point
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.APolarPoint,nameInDictionary,mp.shapes.APolarPoint,polar
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.APolarPoint,nameInDictionary,mp.shapes.APolarPoint,point
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.APolarPoint,nameInDictionary,mp.shapes.APolarPoint,shapes
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.APolarPoint,nameInDictionary,mp.shapes.APolarPoint,a
2,Wed Jul 09 13:32:04 EDT 2025,false,100,liberalMagicNumber,100,100
2,Wed Jul 09 13:32:04 EDT 2025,false,AvatarImpl,variableHasClassType,AvatarImpl,avatar
2,Wed Jul 09 13:32:04 EDT 2025,false,AvatarImpl,variableHasClassType,AvatarImpl,cur
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerOverriddingSignatures,mp.bridge.GuardHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,nameInDictionary,mp.bridge.GuardHead,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerOverriddingSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,nameInDictionary,mp.bridge.GuardHead,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.GuardHead,nameInDictionary,mp.bridge.GuardHead,head
2,Wed Jul 09 13:32:04 EDT 2025,false,upper,nameInDictionary,upper,upper
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public Y:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.ArthurHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public Y:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public Y:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.shapes.RotatingLine
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public Y:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public Y:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,RotateLine,variableHasInterfaceType,RotateLine,line
2,Wed Jul 09 13:32:04 EDT 2025,false,RotateLine,variableHasInterfaceType,RotateLine,left
2,Wed Jul 09 13:32:04 EDT 2025,false,RotateLine,variableHasInterfaceType,RotateLine,right
2,Wed Jul 09 13:32:04 EDT 2025,false,RotateLine,variableHasInterfaceType,RotateLine,getRightLine
2,Wed Jul 09 13:32:04 EDT 2025,false,RotateLine,variableHasInterfaceType,RotateLine,getLeftLine
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.StringShape,nameInDictionary,mp.bridge.StringShape,string
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.StringShape,nameInDictionary,mp.bridge.StringShape,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.StringShape,nameInDictionary,mp.bridge.StringShape,shape
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public X:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.LancelotHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public X:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.ArthurHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public X:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.GuardHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public X:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.GalahadHead
2,Wed Jul 09 13:32:04 EDT 2025,false,readonly  p-v:2 access:public X:int(public ,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.shapes.RotatingLine
2,Wed Jul 09 13:32:04 EDT 2025,false,height,nameInDictionary,height,height
2,Wed Jul 09 13:32:04 EDT 2025,false,KnightTurn,nameInDictionary,KnightTurn,knight
2,Wed Jul 09 13:32:04 EDT 2025,false,KnightTurn,nameInDictionary,KnightTurn,turn
2,Wed Jul 09 13:32:04 EDT 2025,false,@Comp301Tags.BRIDGE_SCENE,expectedInstantiation,@Comp301Tags.BRIDGE_SCENE,main.Assignment1[main.Assignment1],[static public  main:String[]->void]
2,Wed Jul 09 13:32:04 EDT 2025,false,Head,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Head,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Head,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Head,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,Head,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,SOME_ANGLE,nameInDictionary,SOME_ANGLE,some
2,Wed Jul 09 13:32:04 EDT 2025,false,SOME_ANGLE,nameInDictionary,SOME_ANGLE,angle
2,Wed Jul 09 13:32:04 EDT 2025,false,lancelot,nameInDictionary,lancelot,lancelot
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle,nameNotInDictionary,mp.shapes.AScalableRectangle,scalable
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle,nameInDictionary,mp.shapes.AScalableRectangle,a
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle,nameInDictionary,mp.shapes.AScalableRectangle,rectangle
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.AScalableRectangle,nameInDictionary,mp.shapes.AScalableRectangle,shapes
2,Wed Jul 09 13:32:04 EDT 2025,false,START_Y,nameInDictionary,START_Y,y
2,Wed Jul 09 13:32:04 EDT 2025,false,START_Y,nameInDictionary,START_Y,start
2,Wed Jul 09 13:32:04 EDT 2025,false,START_X,nameInDictionary,START_X,x
2,Wed Jul 09 13:32:04 EDT 2025,false,START_X,nameInDictionary,START_X,start
2,Wed Jul 09 13:32:04 EDT 2025,false,guard_const,nameInDictionary,guard_const,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,guard_const,nameNotInDictionary,guard_const,const
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,rectangle
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,guardArea
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,knightArea
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,getGuardArea
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,getRectangle
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\AScalableRectangleInterface.java,variableHasInterfaceType,AScalableRectangleInterface,getKnightArea
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\StringShape.java,variableHasInterfaceType,StringShape,getStringShape
2,Wed Jul 09 13:32:04 EDT 2025,false,cur,nameInDictionary,cur,cur
2,Wed Jul 09 13:32:04 EDT 2025,false,SOME_RAD,nameInDictionary,SOME_RAD,some
2,Wed Jul 09 13:32:04 EDT 2025,false,SOME_RAD,nameNotInDictionary,SOME_RAD,rad
2,Wed Jul 09 13:32:04 EDT 2025,false,rightLine,nameInDictionary,rightLine,right
2,Wed Jul 09 13:32:04 EDT 2025,false,rightLine,nameInDictionary,rightLine,line
2,Wed Jul 09 13:32:04 EDT 2025,false,LINE_HEIGHT,nameInDictionary,LINE_HEIGHT,height
2,Wed Jul 09 13:32:04 EDT 2025,false,LINE_HEIGHT,nameInDictionary,LINE_HEIGHT,line
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.BridgeScene,nameInDictionary,mp.bridge.BridgeScene,scene
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.BridgeScene,nameInDictionary,mp.bridge.BridgeScene,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,speech,nameInDictionary,speech,speech
2,Wed Jul 09 13:32:04 EDT 2025,false,failed:->void,missingSignature,failed:->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ImageShape.java,variableHasInterfaceType,ImageShape,h
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ImageShape.java,variableHasInterfaceType,ImageShape,head
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\ImageShape.java,variableHasInterfaceType,ImageShape,getHead
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,Arthur,expectedGetter,Arthur,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,RIGHT_LINE_X,nameInDictionary,RIGHT_LINE_X,line
2,Wed Jul 09 13:32:04 EDT 2025,false,RIGHT_LINE_X,nameInDictionary,RIGHT_LINE_X,x
2,Wed Jul 09 13:32:04 EDT 2025,false,RIGHT_LINE_X,nameInDictionary,RIGHT_LINE_X,right
2,Wed Jul 09 13:32:04 EDT 2025,false,main.BridgeSceneImpl,nameInDictionary,main.BridgeSceneImpl,main
2,Wed Jul 09 13:32:04 EDT 2025,false,main.BridgeSceneImpl,nameInDictionary,main.BridgeSceneImpl,scene
2,Wed Jul 09 13:32:04 EDT 2025,false,main.BridgeSceneImpl,nameInDictionary,main.BridgeSceneImpl,impl
2,Wed Jul 09 13:32:04 EDT 2025,false,main.BridgeSceneImpl,nameInDictionary,main.BridgeSceneImpl,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,Guard,expectedGetter,Guard,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,KNIGHT_Y,nameInDictionary,KNIGHT_Y,knight
2,Wed Jul 09 13:32:04 EDT 2025,false,KNIGHT_Y,nameInDictionary,KNIGHT_Y,y
2,Wed Jul 09 13:32:04 EDT 2025,false,AREA_X,nameInDictionary,AREA_X,area
2,Wed Jul 09 13:32:04 EDT 2025,false,AREA_X,nameInDictionary,AREA_X,x
2,Wed Jul 09 13:32:04 EDT 2025,false,AREA_WIDTH,nameInDictionary,AREA_WIDTH,width
2,Wed Jul 09 13:32:04 EDT 2025,false,AREA_WIDTH,nameInDictionary,AREA_WIDTH,area
2,Wed Jul 09 13:32:04 EDT 2025,false,Lancelot,expectedGetter,Lancelot,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,approach:@Comp301Tags.AVATAR->void,missingSignature,approach:@Comp301Tags.AVATAR->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl,nameInDictionary,mp.bridge.AvatarImpl,impl
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl,nameInDictionary,mp.bridge.AvatarImpl,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.AvatarImpl,nameInDictionary,mp.bridge.AvatarImpl,avatar
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,nameInDictionary,mp.shapes.RotatingLine,shapes
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,nameInDictionary,mp.shapes.RotatingLine,line
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getAngle:->double
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getRadius:->double
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine,nameInDictionary,mp.shapes.RotatingLine,rotating
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE],missingGetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE],missingSetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE],missingGetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE],missingSetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]
2,Wed Jul 09 13:32:04 EDT 2025,false,leftLine,nameInDictionary,leftLine,left
2,Wed Jul 09 13:32:04 EDT 2025,false,leftLine,nameInDictionary,leftLine,line
2,Wed Jul 09 13:32:04 EDT 2025,false,ImageShape,variableHasInterfaceType,ImageShape,head
2,Wed Jul 09 13:32:04 EDT 2025,false,ImageShape,variableHasInterfaceType,ImageShape,getHead
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.Point,nameInDictionary,mp.shapes.Point,shapes
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.shapes.Point,nameInDictionary,mp.shapes.Point,point
2,Wed Jul 09 13:32:04 EDT 2025,false,gal_const,nameNotInDictionary,gal_const,const
2,Wed Jul 09 13:32:04 EDT 2025,false,gal_const,nameInDictionary,gal_const,gal
2,Wed Jul 09 13:32:04 EDT 2025,false,LINE_TOP_Y,nameInDictionary,LINE_TOP_Y,y
2,Wed Jul 09 13:32:04 EDT 2025,false,LINE_TOP_Y,nameInDictionary,LINE_TOP_Y,top
2,Wed Jul 09 13:32:04 EDT 2025,false,LINE_TOP_Y,nameInDictionary,LINE_TOP_Y,line
2,Wed Jul 09 13:32:04 EDT 2025,false,@Comp301Tags.LOCATABLE,missingSuperType,@Comp301Tags.LOCATABLE,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:32:04 EDT 2025,false,@Comp301Tags.LOCATABLE,missingSuperType,@Comp301Tags.LOCATABLE,mp.bridge.SpeechBubble
2,Wed Jul 09 13:32:04 EDT 2025,false,main.Assignment1,nameInDictionary,main.Assignment1,main
2,Wed Jul 09 13:32:04 EDT 2025,false,main.Assignment1,nameInDictionary,main.Assignment1,assignment
2,Wed Jul 09 13:32:04 EDT 2025,false,@Comp301Tags.ANGLE,expectedInstantiation,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],[public  AvatarImpl:mp.bridge.ImageShape->]
2,Wed Jul 09 13:32:04 EDT 2025,false,gorge,nameInDictionary,gorge,gorge
2,Wed Jul 09 13:32:04 EDT 2025,false,COUNT,nameInDictionary,COUNT,count
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.LancelotHead,nameInDictionary,mp.bridge.LancelotHead,lancelot
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.LancelotHead,peerDuplicatedSignatures,mp.bridge.LancelotHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.LancelotHead,nameInDictionary,mp.bridge.LancelotHead,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.LancelotHead,nameInDictionary,mp.bridge.LancelotHead,head
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.LancelotHead,peerDuplicatedSignatures,mp.bridge.LancelotHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.SpeechBubble,nameInDictionary,mp.bridge.SpeechBubble,bubble
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.SpeechBubble,nameInDictionary,mp.bridge.SpeechBubble,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.SpeechBubble,nameInDictionary,mp.bridge.SpeechBubble,speech
2,Wed Jul 09 13:32:04 EDT 2025,false,guard,nameInDictionary,guard,guard
2,Wed Jul 09 13:32:04 EDT 2025,false,[],publicMethodsOverride,[]
2,Wed Jul 09 13:32:04 EDT 2025,false,Angle,variableHasInterfaceType,Angle,legs
2,Wed Jul 09 13:32:04 EDT 2025,false,Angle,variableHasInterfaceType,Angle,getLegs
2,Wed Jul 09 13:32:04 EDT 2025,false,Angle,expectedSetter,Angle,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,Angle,variableHasInterfaceType,Angle,getArms
2,Wed Jul 09 13:32:04 EDT 2025,false,Angle,variableHasInterfaceType,Angle,arms
2,Wed Jul 09 13:32:04 EDT 2025,false,Angle,expectedGetter,Angle,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
2,Wed Jul 09 13:32:04 EDT 2025,false,main.RunSS25A2Tests,nameInDictionary,main.RunSS25A2Tests,main
2,Wed Jul 09 13:32:04 EDT 2025,false,main.RunSS25A2Tests,nameInDictionary,main.RunSS25A2Tests,tests
2,Wed Jul 09 13:32:04 EDT 2025,false,main.RunSS25A2Tests,nameInDictionary,main.RunSS25A2Tests,run
2,Wed Jul 09 13:32:04 EDT 2025,false,main.RunSS25A2Tests,nameInDictionary,main.RunSS25A2Tests,a
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.Avatar,nameInDictionary,mp.bridge.Avatar,bridge
2,Wed Jul 09 13:32:04 EDT 2025,false,mp.bridge.Avatar,nameInDictionary,mp.bridge.Avatar,avatar
2,Wed Jul 09 13:32:04 EDT 2025,false,width,nameInDictionary,width,width
2,Wed Jul 09 13:32:04 EDT 2025,false,percentage,nameInDictionary,percentage,percentage
2,Wed Jul 09 13:32:04 EDT 2025,false,say:String->void,missingSignature,say:String->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,scale:double->void//EC,missingSignature,scale:double->void//EC,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,scale:double->void//EC,missingSignature,scale:double->void//EC,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,scale:double->void//EC,missingSignature,scale:double->void//EC,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,scale:double->void//EC,missingSignature,scale:double->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,scale:double->void//EC,missingSignature,scale:double->void//EC,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,scale:double->void//EC,missingSignature,scale:double->void//EC,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
2,Wed Jul 09 13:32:04 EDT 2025,false,[public  getLeftLine:->mp.shapes.RotatingLine,publicMethodsDoNotOverride,[public  getLeftLine:->mp.shapes.RotatingLine, public  getRightLine:->mp.shapes.RotatingLine, public  getRectangle:->mp.shapes.AScalableRectangle]
2,Wed Jul 09 13:32:04 EDT 2025,false,[public  approach:mp.bridge.AvatarImpl->void,publicMethodsDoNotOverride,[public  approach:mp.bridge.AvatarImpl->void, public  say:String->void, public  getKnightArea:->mp.shapes.AScalableRectangle, public  getGuardArea:->mp.shapes.AScalableRectangle, public  getGorge:->mp.shapes.Gorge]
2,Wed Jul 09 13:32:04 EDT 2025,false,rectangle,nameInDictionary,rectangle,rectangle
2,Wed Jul 09 13:32:04 EDT 2025,false,Galahad,expectedGetter,Galahad,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,arthur,nameInDictionary,arthur,arthur
2,Wed Jul 09 13:32:04 EDT 2025,false,line,nameInDictionary,line,line
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,right
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,rightLine
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,leftLine
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,left
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getRightLine
2,Wed Jul 09 13:32:04 EDT 2025,false,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getLeftLine
2,Wed Jul 09 13:32:04 EDT 2025,false,point,nameInDictionary,point,point
2,Wed Jul 09 13:32:04 EDT 2025,false,robin,nameInDictionary,robin,robin
2,Wed Jul 09 13:32:04 EDT 2025,false,passed:->void,missingSignature,passed:->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
2,Wed Jul 09 13:32:04 EDT 2025,false,say,thenBranching,say
2,Wed Jul 09 13:32:57 EDT 2025,true,mp\bridge\Angle.java,expectedTypes,[main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getLeftLine:->mp.shapes.RotateLine, default getRightLine:->mp.shapes.RotateLine]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,properties,Properties:[readonly  p-v:5 access:package RightLine:mp.shapes.RotateLine(default , null), readonly  p-v:5 access:package LeftLine:mp.shapes.RotateLine(default , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,superTypes,[mp.shapes.Moveable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\Angle.java,tags,mp.bridge.Angle,@Comp301Tags.ANGLE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,expectedTypes,[main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,variables,Variables:[private String fn, private int x, private int y]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.GalahadHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GalahadHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GalahadHead, public, private, 3, mp.bridge.GalahadHead, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,properties,Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,interfaces,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,tags,mp.bridge.GalahadHead,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.ArthurHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.ArthurHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.ArthurHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.shapes.RotatingLine
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.shapes.RotatingLine
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,missingMethodCall,@Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE,No method,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getX:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setX:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  getY:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GalahadHead.java,peerOverriddingSignatures,mp.bridge.ArthurHead,public  setY:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,variables,Variables:[private String fn, private int x, private int y]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.ArthurHead, public, public, 0, main.Assignment2, null ), (mp.bridge.ArthurHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.ArthurHead, public, private, 3, mp.bridge.ArthurHead, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,properties,Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,interfaces,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,tags,mp.bridge.ArthurHead,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ArthurHead.java,missingMethodCall,@Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE,No method,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,variables,Variables:[default int rightlinex, default int linetopy, default int lineheight, default int c1, default int c2, default int upper, default int lower, default mp.shapes.RotateLine leftLine, default mp.shapes.RotateLine rightLine, default mp.shapes.AScalableRectangleInterface rectangle]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Gorge, public, private, 3, mp.shapes.Gorge, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine, public  getRectangle:->mp.shapes.AScalableRectangleInterface]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,properties,Properties:[readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public Rectangle:mp.shapes.AScalableRectangleInterface(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:10 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.8 Average Local Assignments per Variable:2.8
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,interfaces,[mp.shapes.GetRect]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null),Gorge,mp.bridge.VShape
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null),Gorge,mp.bridge.VShape
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,peerOverriddingSignatures,mp.bridge.VShape,public  getLeftLine:->mp.shapes.RotateLine,[mp.shapes.Get]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Gorge.java,peerOverriddingSignatures,mp.bridge.VShape,public  getRightLine:->mp.shapes.RotateLine,[mp.shapes.Get]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,variables,Variables:[private mp.shapes.Point point, private int a, private int b, private static double UNIT]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.RotatingLine, public, private, 3, mp.shapes.RotatingLine, null ), (mp.shapes.RotatingLine, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.VShape, null ), (mp.shapes.RotatingLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  rotate:int->void, public  move:int;int->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getRadius:->double, public  getAngle:->double]Setters:[public  setX:int->void, public  setY:int->void, public  setRadius:double->void, public  setAngle:double->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,properties,Properties:[editable, g-s:0 p-v:3 access:public Radius:double(public ,public ), editable, g-s:0 p-v:3 access:public Angle:double(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), readonly  p-v:3 access:public Height:int(public , null), readonly  p-v:3 access:public Width:int(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:12 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.6666666666666666 ReadOnly Access Properties Fraction:0.3333333333333333 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:1.0 Average Local References per Variable:5.25 Average Local Assignments per Variable:5.25
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,interfaces,[mp.shapes.RotateLine, mp.shapes.Moveable, mp.shapes.PolarPointInterface]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,tags,mp.shapes.RotatingLine,@Comp301Tags.LOCATABLE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GuardHead
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,readonly  p-v:3 access:public Height:int(public , null),RotatingLine,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,readonly  p-v:3 access:public Width:int(public , null),RotatingLine,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Radius:double(public ,public ),RotatingLine,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Angle:double(public ,public ),RotatingLine,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,missingMethodCall,No method,mp.shapes.RotatingLine:[@Comp301Tags.LOCATABLE]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.VShape,public  move:int;int->void,[mp.shapes.Moveable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  getWidth:->int,[mp.shapes.BoundedShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  getHeight:->int,[mp.shapes.BoundedShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotatingLine.java,peerOverriddingSignatures,mp.bridge.AvatarImpl,public  move:int;int->void,[mp.shapes.Moveable]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,variables,Variables:[default static mp.bridge.BridgeScene scene]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,accessModifiersUsed,Access Modifiers Used: [(main.StaticFactoryClass, public, private, 3, main.StaticFactoryClass, null ), (main.StaticFactoryClass, public, package, 2, main.Assignment2, null ), (main.StaticFactoryClass, public, public, 0, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,methods,NonGetterFunctions:[static public  bridgeSceneFactoryMethod:->mp.bridge.BridgeScene, static public  legsFactoryMethod:->mp.bridge.Angle]NonSetterProcedures:[]Getters:[]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,properties,Properties:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:2 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:1 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,interfaces,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,main\StaticFactoryClass.java,tags,main.StaticFactoryClass,@Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,variables,Variables:[private String fn, private int x, private int y]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.GuardHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.GuardHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.GuardHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.GuardHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GuardHead, public, private, 3, mp.bridge.GuardHead, null ), (mp.bridge.GuardHead, public, package, 2, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,properties,Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,interfaces,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,tags,mp.bridge.GuardHead,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GuardHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GuardHead,mp.shapes.APolarPoint
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,missingMethodCall,@Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE,No method,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getX:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setX:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getY:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setY:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getX:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setX:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  getY:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.bridge.LancelotHead,public  setY:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getX:->int,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setX:int->void,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getY:->int,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\GuardHead.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setY:int->void,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,variables,Variables:[default double radius, default double angle]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.APolarPoint, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.APolarPoint, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.APolarPoint, public, private, 3, mp.shapes.APolarPoint, null ), (mp.shapes.APolarPoint, public, public, 0, main.Assignment2, null ), (mp.shapes.APolarPoint, public, public, 0, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getX:->int, public  getY:->int, public  getAngle:->double, public  getRadius:->double]Setters:[public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,properties,Properties:[readonly  p-v:2 access:public Radius:double(public , null), readonly  p-v:2 access:public Angle:double(public , null), editable, g-s:0 p-v:5 access:public X:int(public ,public ), editable, g-s:0 p-v:5 access:public Y:int(public ,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.5 ReadOnly Access Properties Fraction:0.5 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:5.0 Average Local Assignments per Variable:5.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,interfaces,[mp.shapes.Point, mp.shapes.Locatable, mp.shapes.PolarPointInterface]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public X:int(public ,public ),APolarPoint,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:5 access:public Y:int(public ,public ),APolarPoint,mp.shapes.AScalableRectangle
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getAngle:->double,[mp.shapes.Locatable, mp.shapes.PolarPointInterface]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getRadius:->double,[mp.shapes.Locatable, mp.shapes.PolarPointInterface]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setX:int->void,[mp.shapes.Locatable, mp.shapes.PolarPointInterface]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setY:int->void,[mp.shapes.Locatable, mp.shapes.PolarPointInterface]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getText:->String]Setters:[default setText:String->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,properties,Properties:[editable, g-s:0 p-v:5 access:package Text:String(default ,default )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,superTypes,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\StringShape.java,tags,mp.bridge.StringShape,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getAngle:->double, public  getRadius:->double]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,properties,Properties:[readonly  p-v:5 access:public Radius:double(public , null), readonly  p-v:5 access:public Angle:double(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,superTypes,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\PolarPointInterface.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,variables,Variables:[private mp.bridge.ImageShape head, private mp.bridge.StringShape speech, private mp.bridge.Angle arms, private mp.bridge.Angle legs]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.AvatarImpl, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.AvatarImpl, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.AvatarImpl, public, public, 0, main.Assignment2, null ), (mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,methods,NonGetterFunctions:[]NonSetterProcedures:[private  layoutAtOrigin:->void, public  move:int;int->void]Getters:[public  getHead:->mp.bridge.ImageShape, public  getStringShape:->mp.bridge.StringShape, public  getArms:->mp.bridge.Angle, public  getLegs:->mp.bridge.Angle]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,properties,Properties:[readonly  p-v:3 access:public Legs:mp.bridge.Angle(public , null), readonly  p-v:3 access:public Head:mp.bridge.ImageShape(public , null), readonly  p-v:3 access:public StringShape:mp.bridge.StringShape(public , null), readonly  p-v:3 access:public Arms:mp.bridge.Angle(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:1 Public Methods Fraction:0.8333333333333334 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.16666666666666666 Average Method Access:0.5 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:4.5 Average Local References per Variable:4.5 Average Local Assignments per Variable:4.5
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,interfaces,[mp.bridge.Avatar, mp.shapes.Moveable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,tags,mp.bridge.AvatarImpl,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedMethodCall,@Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE,Some method (AvatarImpl:mp.bridge.ImageShape->),mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void]Getters:[]Setters:[public  setHeight:int->void, public  setWidth:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,properties,Properties:[writeonly  p-v:5 access:public Height:int( null,public ), writeonly  p-v:5 access:public Width:int( null,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:1.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,superTypes,[mp.shapes.BoundedShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangleInterface.java,tags,mp.shapes.AScalableRectangleInterface,@Comp301Tags.BOUNDED_SHAPE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getRectangle:->mp.shapes.AScalableRectangleInterface]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,properties,Properties:[readonly  p-v:5 access:public Rectangle:mp.shapes.AScalableRectangleInterface(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:1 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,superTypes,[mp.shapes.Get]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\GetRect.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getImageFileName:->String]Setters:[default setImageFileName:String->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,properties,Properties:[editable, g-s:0 p-v:5 access:package ImageFileName:String(default ,default )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,superTypes,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\ImageShape.java,tags,mp.bridge.ImageShape,@Comp301Tags.BOUNDED_SHAPE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,properties,Properties:[readonly  p-v:5 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:5 access:public LeftLine:mp.shapes.RotateLine(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,superTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Get.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,variables,Variables:[default int x, default int y, default int width, default int height, default int percentConversion]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.AScalableRectangle, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.Assignment2, null ), (mp.shapes.AScalableRectangle, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AScalableRectangle, public, private, 3, mp.shapes.AScalableRectangle, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int]Setters:[public  setHeight:int->void, public  setWidth:int->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,properties,Properties:[editable, g-s:0 p-v:2 access:public X:int(public ,public ), editable, g-s:0 p-v:2 access:public Y:int(public ,public ), editable, g-s:0 p-v:2 access:public Height:int(public ,public ), editable, g-s:0 p-v:2 access:public Width:int(public ,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:9 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:8 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:5 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:4.4 Average Local Assignments per Variable:4.4
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,interfaces,[mp.shapes.AScalableRectangleInterface, mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,tags,mp.shapes.AScalableRectangle,@Comp301Tags.BOUNDED_SHAPE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\AScalableRectangle.java,missingMethodCall,No method,mp.shapes.AScalableRectangle:[@Comp301Tags.BOUNDED_SHAPE]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Locatable, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.Locatable, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Locatable, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Locatable, public, public, 0, main.Assignment2, null ), (mp.shapes.Locatable, public, public, 0, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getX:->int, default getY:->int]Setters:[default setX:int->void, default setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,properties,Properties:[editable, g-s:0 p-v:5 access:package X:int(default ,default ), editable, g-s:0 p-v:5 access:package Y:int(default ,default )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,superTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Locatable.java,tags,mp.shapes.Locatable,@Comp301Tags.LOCATABLE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Point, public, package, 2, mp.shapes.RotatingLine, null ), (mp.shapes.Point, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Point, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Point, public, public, 0, main.Assignment2, null ), (mp.shapes.Point, public, public, 0, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getX:->int, public  getY:->int, public  getAngle:->double, public  getRadius:->double]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,properties,Properties:[readonly  p-v:5 access:public Radius:double(public , null), readonly  p-v:5 access:public Angle:double(public , null), readonly  p-v:5 access:public X:int(public , null), readonly  p-v:5 access:public Y:int(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,superTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Point.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getWidth:->int, default getHeight:->int]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,properties,Properties:[readonly  p-v:5 access:package Height:int(default , null), readonly  p-v:5 access:package Width:int(default , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,superTypes,[mp.shapes.Locatable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\BoundedShape.java,tags,mp.shapes.BoundedShape,@Comp301Tags.BOUNDED_SHAPE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,variables,Variables:[private mp.shapes.RotateLine left, private mp.shapes.RotateLine right]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.VShape, public, private, 3, mp.bridge.VShape, null ), (mp.bridge.VShape, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.VShape, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.VShape, public, public, 0, main.Assignment2, null ), (mp.bridge.VShape, public, package, 2, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,properties,Properties:[readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:3.0 Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,interfaces,[mp.bridge.Angle, mp.shapes.Get]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\VShape.java,tags,mp.bridge.VShape,@Comp301Tags.ANGLE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Moveable, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.VShape, null ), (mp.shapes.Moveable, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Moveable, public, public, 0, main.Assignment2, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,properties,Properties:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,superTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\Moveable.java,tags,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,accessModifiersUsed,Access Modifiers Used: []
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,properties,Properties:[readonly  p-v:5 access:public KnightTurn:boolean(public , null), readonly  p-v:5 access:public Gorge:mp.shapes.Gorge(public , null), readonly  p-v:5 access:public Occupied:boolean(public , null), readonly  p-v:5 access:package Arthur:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public KnightArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:5 access:package Guard:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Lancelot:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public GuardArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:5 access:package Galahad:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Robin:mp.bridge.Avatar(default , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:14 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:5 Public Methods Fraction:0.6428571428571429 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.35714285714285715 Private  Methods Fraction:0.0 Average Method Access:0.7142857142857143 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:10 Public Properties Fraction:0.5 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.5 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:1.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,superTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\BridgeScene.java,tags,mp.bridge.BridgeScene,@Comp301Tags.BRIDGE_SCENE
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,variables,Variables:[private String fn, private int x, private int y]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.LancelotHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.LancelotHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.LancelotHead, public, private, 3, mp.bridge.LancelotHead, null ), (mp.bridge.LancelotHead, public, public, 0, main.Assignment2, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,properties,Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,superTypes,None
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,interfaces,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,tags,mp.bridge.LancelotHead,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\bridge\LancelotHead.java,missingMethodCall,@Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE,No method,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,variables,Variables:[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.RotateLine, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.RotateLine, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.RotateLine, public, public, 0, main.Assignment2, null )]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,methods,NonGetterFunctions:[]NonSetterProcedures:[default rotate:int->void]Getters:[default getHeight:->int]Setters:[default setRadius:double->void, default setAngle:double->void]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,properties,Properties:[writeonly  p-v:5 access:package Radius:double( null,default ), writeonly  p-v:5 access:package Angle:double( null,default ), readonly  p-v:5 access:package Height:int(default , null)]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:3 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.3333333333333333 WriteOnly  Properties Fraction:0.6666666666666666 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,innerTypes,[]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,superTypes,[mp.shapes.BoundedShape, mp.shapes.Moveable]
2,Wed Jul 09 13:33:18 EDT 2025,true,mp\shapes\RotateLine.java,tags,mp.shapes.RotateLine,@Comp301Tags.BOUNDED_SHAPE
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,variables,Variables:[private static int MAX_PRINTED_TRACES, private static int MAX_TRACES, private static int PROCESS_TIMEOUT_S]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,accessModifiersUsed,Access Modifiers Used: [(main.RunSS25A2Tests, public, private, 3, main.RunSS25A2Tests, null )]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,methods,NonGetterFunctions:[]NonSetterProcedures:[static public  main:String[]->void]Getters:[]Setters:[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,properties,Properties:[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.0 Average Local References per Variable:1.0 Average Local Assignments per Variable:1.0
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,innerTypes,[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,superTypes,None
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,interfaces,[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\RunSS25A2Tests.java,tags,None
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,variables,Variables:[private String text, private int a, private int b]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.SpeechBubble, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.SpeechBubble, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.SpeechBubble, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.SpeechBubble, public, private, 3, mp.bridge.SpeechBubble, null ), (mp.bridge.SpeechBubble, public, public, 0, main.Assignment2, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getText:->String, public  getX:->int, public  getY:->int]Setters:[public  setText:String->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,properties,Properties:[editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), editable, g-s:0 p-v:3 access:public Text:String(public ,public )]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,innerTypes,[]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,superTypes,None
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,interfaces,[mp.bridge.StringShape]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,tags,mp.bridge.SpeechBubble,@Comp301Tags.LOCATABLE
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\SpeechBubble.java,missingMethodCall,No method,mp.bridge.SpeechBubble:[@Comp301Tags.LOCATABLE]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,variables,Variables:[public static int SOME_RAD, public static double SOME_ANGLE, public static int START_X, public static int START_Y, public static int D, public static int COUNT, public static long SLEEP_MS]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,accessModifiersUsed,Access Modifiers Used: [(main.Assignment2, public, package, 2, main.RunSS25A2Tests, null ), (main.Assignment2, public, private, 3, main.Assignment2, null )]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,methods,NonGetterFunctions:[]NonSetterProcedures:[static public  animateLine:->void, static public  main:String[]->void]Getters:[]Setters:[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,properties,Properties:[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:7 Public Variables Fraction:1.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:0.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.1428571428571428 Average Local References per Variable:1.1428571428571428 Average Local Assignments per Variable:1.1428571428571428
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,innerTypes,[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,superTypes,None
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,interfaces,[]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,tags,main.Assignment2,main.Assignment2
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,missingMethodCall,@Comp301Tags.FACTORY_CLASS!@consoleSceneViewFactoryMethod:->.*,Method:main:String[]->void,main.Assignment2:[main.Assignment2]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,missingMethodCall,Method:main:String[]->void,main.Assignment2:[main.Assignment2]
2,Wed Jul 09 13:33:19 EDT 2025,true,main\Assignment2.java,expectedMethodCall,@Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,Method:main:String[]->void,main.Assignment2:[main.Assignment2]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,variables,Variables:[private String fn, private int x, private int y]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.RobinHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.RobinHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.RobinHead, public, private, 3, mp.bridge.RobinHead, null ), (mp.bridge.RobinHead, public, public, 0, main.Assignment2, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.AvatarImpl, null )]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,methods,NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,properties,Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,innerTypes,[]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,superTypes,None
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,interfaces,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,tags,mp.bridge.RobinHead,@Comp301Tags.AVATAR
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,missingMethodCall,@Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE,No method,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:33:19 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,variables,Variables:[private mp.bridge.Avatar arthur, private mp.bridge.Avatar lancelot, private mp.bridge.Avatar robin, private mp.bridge.Avatar galahad, private mp.bridge.Avatar guard, public static int SOME_X, public static int SOME_Y, public static int L_CONST, public static int R_CONST, public static int GAL_CONST, public static int GUARD_CONST, private mp.shapes.Gorge gorge, private mp.bridge.Avatar cur, private mp.shapes.AScalableRectangleInterface knightArea, private mp.shapes.AScalableRectangleInterface guardArea, private boolean knightTurn, private static int AREA_X, private static int KNIGHT_Y, private static int GUARD_Y, private static int AREA_WIDTH, private static int AREA_HEIGHT, private boolean occupied, private static int GORGE_X, private static int gorgey, default int diff]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,accessModifiersUsed,Access Modifiers Used: [(main.BridgeSceneImpl, public, private, 3, main.BridgeSceneImpl, null )]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[public  getArthur:->mp.bridge.Avatar, public  getLancelot:->mp.bridge.Avatar, public  getRobin:->mp.bridge.Avatar, public  getGalahad:->mp.bridge.Avatar, public  getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,properties,Properties:[readonly  p-v:3 access:public KnightTurn:boolean(public , null), readonly  p-v:3 access:public Gorge:mp.shapes.Gorge(public , null), readonly  p-v:3 access:public Occupied:boolean(public , null), readonly  p-v:3 access:public Arthur:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public KnightArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:3 access:public Guard:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Lancelot:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public GuardArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:3 access:public Galahad:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Robin:mp.bridge.Avatar(public , null)]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:14 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:25 Public Variables Fraction:0.24 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.04 Private  Variable Fraction:0.72 Average Variable Access:2.24 Number of Properties:10 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.7058823529411766 Average Local References per Variable:3.16 Average Local Assignments per Variable:3.16
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,innerTypes,[]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,superTypes,None
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,interfaces,[mp.bridge.BridgeScene]
2,Wed Jul 09 13:34:05 EDT 2025,true,main\BridgeSceneImpl.java,tags,main.BridgeSceneImpl,@Comp301Tags.BRIDGE_SCENE
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\AvatarImpl.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  move:int;int->void,[mp.shapes.Moveable]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getWidth:->int,[mp.shapes.BoundedShape, mp.shapes.Locatable]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getHeight:->int,[mp.shapes.BoundedShape, mp.shapes.Locatable]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setX:int->void,[mp.shapes.BoundedShape, mp.shapes.Locatable]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setY:int->void,[mp.shapes.BoundedShape, mp.shapes.Locatable]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getX:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setX:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getY:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setY:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.shapes.Locatable, mp.bridge.ImageShape]
2,Wed Jul 09 13:34:05 EDT 2025,true,mp\bridge\RobinHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.shapes.Locatable, mp.bridge.ImageShape]
3,Wed Jul 09 15:34:29 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getAngle:->double,[mp.shapes.PolarPointInterface, mp.shapes.Locatable]
3,Wed Jul 09 15:34:29 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  getRadius:->double,[mp.shapes.PolarPointInterface, mp.shapes.Locatable]
3,Wed Jul 09 15:34:29 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setX:int->void,[mp.shapes.PolarPointInterface, mp.shapes.Locatable]
3,Wed Jul 09 15:34:29 EDT 2025,true,mp\shapes\APolarPoint.java,peerOverriddingSignatures,mp.shapes.RotatingLine,public  setY:int->void,[mp.shapes.PolarPointInterface, mp.shapes.Locatable]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,variables,Variables:[]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.Avatar, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.Avatar, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.Avatar, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.Avatar, public, public, 0, main.Assignment2, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,methods,NonGetterFunctions:[]NonSetterProcedures:[default move:int;int->void]Getters:[default getHead:->mp.bridge.ImageShape, default getStringShape:->mp.bridge.StringShape, default getArms:->mp.bridge.Angle, default getLegs:->mp.bridge.Angle]Setters:[]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,properties,Properties:[readonly  p-v:5 access:package Legs:mp.bridge.Angle(default , null), readonly  p-v:5 access:package Head:mp.bridge.ImageShape(default , null), readonly  p-v:5 access:package StringShape:mp.bridge.StringShape(default , null), readonly  p-v:5 access:package Arms:mp.bridge.Angle(default , null)]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:5 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:5 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,innerTypes,[]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,superTypes,[mp.shapes.Moveable]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\Avatar.java,tags,mp.bridge.Avatar,@Comp301Tags.AVATAR
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\GalahadHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.GalahadHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.GalahadHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GalahadHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GalahadHead, public, private, 3, mp.bridge.GalahadHead, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\ArthurHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.ArthurHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.ArthurHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.ArthurHead, public, private, 3, mp.bridge.ArthurHead, null ), (mp.bridge.ArthurHead, public, public, 0, main.Assignment2, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\ArthurHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getX:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\ArthurHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setX:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\ArthurHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  getY:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\ArthurHead.java,peerOverriddingSignatures,mp.bridge.RobinHead,public  setY:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\shapes\RotatingLine.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.RotatingLine, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.RotatingLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotatingLine, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.RotatingLine, public, private, 3, mp.shapes.RotatingLine, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,main\StaticFactoryClass.java,accessModifiersUsed,Access Modifiers Used: [(main.StaticFactoryClass, public, package, 2, main.BridgeSceneImpl, null ), (main.StaticFactoryClass, public, private, 3, main.StaticFactoryClass, null ), (main.StaticFactoryClass, public, public, 0, mp.bridge.AvatarImpl, null ), (main.StaticFactoryClass, public, package, 2, main.Assignment2, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\GuardHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.GuardHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.GuardHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.GuardHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GuardHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GuardHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.GuardHead, public, private, 3, mp.bridge.GuardHead, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\shapes\APolarPoint.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.APolarPoint, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.APolarPoint, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.APolarPoint, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.APolarPoint, public, public, 0, main.Assignment2, null ), (mp.shapes.APolarPoint, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.APolarPoint, public, private, 3, mp.shapes.APolarPoint, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\StringShape.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.StringShape, public, public, 0, main.BridgeSceneImpl, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\AvatarImpl.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.AvatarImpl, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.AvatarImpl, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null ), (mp.bridge.AvatarImpl, public, public, 0, main.Assignment2, null ), (mp.bridge.AvatarImpl, public, public, 0, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\shapes\AScalableRectangle.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.AScalableRectangle, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.AScalableRectangle, public, private, 3, mp.shapes.AScalableRectangle, null ), (mp.shapes.AScalableRectangle, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.Assignment2, null ), (mp.shapes.AScalableRectangle, public, package, 2, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\shapes\Locatable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Locatable, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.Locatable, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Locatable, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.Locatable, public, public, 0, main.Assignment2, null ), (mp.shapes.Locatable, public, package, 2, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\shapes\Point.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Point, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.Point, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Point, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.Point, public, public, 0, main.Assignment2, null ), (mp.shapes.Point, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Point, public, package, 2, mp.shapes.RotatingLine, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\VShape.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.VShape, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.VShape, public, private, 3, mp.bridge.VShape, null ), (mp.bridge.VShape, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.VShape, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.VShape, public, public, 0, main.Assignment2, null ), (mp.bridge.VShape, public, public, 0, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\shapes\Moveable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Moveable, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.Moveable, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.VShape, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.AvatarImpl, null ), (mp.shapes.Moveable, public, public, 0, main.Assignment2, null ), (mp.shapes.Moveable, public, package, 2, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:43 EDT 2025,true,mp\bridge\LancelotHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.LancelotHead, public, private, 3, mp.bridge.LancelotHead, null ), (mp.bridge.LancelotHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.LancelotHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.LancelotHead, public, public, 0, main.Assignment2, null ), (mp.bridge.LancelotHead, public, public, 0, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:44 EDT 2025,true,mp\shapes\RotateLine.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.RotateLine, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.RotateLine, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.RotateLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotateLine, public, package, 2, mp.shapes.Gorge, null )]
3,Wed Jul 09 15:34:44 EDT 2025,true,mp\bridge\SpeechBubble.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.SpeechBubble, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.SpeechBubble, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.SpeechBubble, public, public, 0, main.Assignment2, null ), (mp.bridge.SpeechBubble, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.SpeechBubble, public, private, 3, mp.bridge.SpeechBubble, null )]
3,Wed Jul 09 15:34:44 EDT 2025,true,main\Assignment2.java,accessModifiersUsed,Access Modifiers Used: [(main.Assignment2, public, private, 3, main.Assignment2, null ), (main.Assignment2, public, package, 2, main.RunSS25A2Tests, null )]
3,Wed Jul 09 15:34:44 EDT 2025,true,mp\bridge\RobinHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.RobinHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.RobinHead, public, private, 3, mp.bridge.RobinHead, null ), (mp.bridge.RobinHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.RobinHead, public, public, 0, main.Assignment2, null ), (mp.bridge.RobinHead, public, public, 0, mp.shapes.Gorge, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,main\StaticFactoryClass.java,accessModifiersUsed,Access Modifiers Used: [(main.StaticFactoryClass, public, private, 3, main.StaticFactoryClass, null ), (main.StaticFactoryClass, public, package, 2, main.Assignment2, null ), (main.StaticFactoryClass, public, public, 0, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.GuardHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.GuardHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.GuardHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.GuardHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GuardHead, public, private, 3, mp.bridge.GuardHead, null ), (mp.bridge.GuardHead, public, package, 2, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\LancelotHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.LancelotHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.LancelotHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.LancelotHead, public, private, 3, mp.bridge.LancelotHead, null ), (mp.bridge.LancelotHead, public, public, 0, main.Assignment2, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.bridge.ImageShape, mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.bridge.ImageShape, mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.bridge.ImageShape, mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.bridge.ImageShape, mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.bridge.ImageShape, mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.bridge.ImageShape, mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.GalahadHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GalahadHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GalahadHead, public, private, 3, mp.bridge.GalahadHead, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,expectedTypes,[main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\AvatarImpl.java,peerDuplicatedSignatures,mp.bridge.VShape,public  move:int;int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\AvatarImpl.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.AvatarImpl, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.AvatarImpl, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.AvatarImpl, public, public, 0, main.Assignment2, null ), (mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\Angle.java,expectedTypes,[main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\Gorge.java,peerDuplicatedSignatures,mp.bridge.VShape,public  getRightLine:->mp.shapes.RotateLine
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\Gorge.java,peerDuplicatedSignatures,mp.bridge.VShape,public  getLeftLine:->mp.shapes.RotateLine
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\Point.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Point, public, package, 2, mp.shapes.RotatingLine, null ), (mp.shapes.Point, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Point, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Point, public, public, 0, main.Assignment2, null ), (mp.shapes.Point, public, public, 0, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,main\Assignment2.java,accessModifiersUsed,Access Modifiers Used: [(main.Assignment2, public, package, 2, main.RunSS25A2Tests, null ), (main.Assignment2, public, private, 3, main.Assignment2, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\Moveable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Moveable, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.VShape, null ), (mp.shapes.Moveable, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Moveable, public, public, 0, main.Assignment2, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\VShape.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.VShape, public, private, 3, mp.bridge.VShape, null ), (mp.bridge.VShape, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.VShape, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.VShape, public, public, 0, main.Assignment2, null ), (mp.bridge.VShape, public, package, 2, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\RobinHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.RobinHead, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.RobinHead, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.RobinHead, public, private, 3, mp.bridge.RobinHead, null ), (mp.bridge.RobinHead, public, public, 0, main.Assignment2, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.ArthurHead, public, public, 0, main.Assignment2, null ), (mp.bridge.ArthurHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.ArthurHead, public, private, 3, mp.bridge.ArthurHead, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getImageFileName:->String
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\AScalableRectangle.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.AScalableRectangle, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.AScalableRectangle, public, public, 0, main.Assignment2, null ), (mp.shapes.AScalableRectangle, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AScalableRectangle, public, private, 3, mp.shapes.AScalableRectangle, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\StringShape.java,accessModifiersUsed,Access Modifiers Used: []
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\bridge\SpeechBubble.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.SpeechBubble, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.SpeechBubble, public, public, 0, mp.shapes.Gorge, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.VShape, null ), (mp.bridge.SpeechBubble, public, public, 0, main.StaticFactoryClass, null ), (mp.bridge.SpeechBubble, public, private, 3, mp.bridge.SpeechBubble, null ), (mp.bridge.SpeechBubble, public, public, 0, main.Assignment2, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.ArthurHead,public  setY:int->void,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.RobinHead,public  setX:int->void,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.RobinHead,public  getY:->int,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.ArthurHead,public  setX:int->void,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.RobinHead,public  setY:int->void,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.ArthurHead,public  getY:->int,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.RobinHead,public  getX:->int,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.ArthurHead,public  getX:->int,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.Locatable],peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.BoundedShape, mp.shapes.Locatable],peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  getHeight:->int,[mp.shapes.BoundedShape, mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,[mp.shapes.BoundedShape, mp.shapes.Locatable],peerOverriddingSignatures,mp.shapes.AScalableRectangle,public  getWidth:->int,[mp.shapes.BoundedShape, mp.shapes.Locatable]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\APolarPoint.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.APolarPoint, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.APolarPoint, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.APolarPoint, public, private, 3, mp.shapes.APolarPoint, null ), (mp.shapes.APolarPoint, public, public, 0, main.Assignment2, null ), (mp.shapes.APolarPoint, public, public, 0, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\Locatable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Locatable, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.Locatable, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.Locatable, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.Locatable, public, public, 0, main.Assignment2, null ), (mp.shapes.Locatable, public, public, 0, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotateLine.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.RotateLine, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.RotateLine, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.RotateLine, public, public, 0, main.Assignment2, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getRadius:->double
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.VShape,public  move:int;int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getAngle:->double
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getX:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getWidth:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setX:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.AvatarImpl,public  move:int;int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.RotatingLine, public, private, 3, mp.shapes.RotatingLine, null ), (mp.shapes.RotatingLine, public, package, 2, mp.shapes.Gorge, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.VShape, null ), (mp.shapes.RotatingLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.AvatarImpl, null )]
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getHeight:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.APolarPoint,public  setY:int->void
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
4,Wed Jul 09 15:55:26 EDT 2025,false,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.shapes.AScalableRectangle,public  setY:int->void
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\Avatar.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.Avatar, public, public, 0, main.BridgeSceneImpl, null )]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\Avatar.java,methods,NonGetterFunctions:[]NonSetterProcedures:[default move:int;int->void, default scroll:int;int->void]Getters:[default getHead:->mp.bridge.ImageShape, default getStringShape:->mp.bridge.StringShape, default getArms:->mp.bridge.Angle, default getLegs:->mp.bridge.Angle]Setters:[]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\Avatar.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:6 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\shapes\Get.java,tags,mp.shapes.Get,@Comp301Tags.ANGLE
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\shapes\APolarPoint.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.APolarPoint, public, private, 3, mp.shapes.APolarPoint, null )]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\shapes\APolarPoint.java,tags,mp.shapes.APolarPoint,@Comp301Tags.LOCATABLE
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\shapes\Moveable.java,accessModifiersUsed,Access Modifiers Used: [(mp.shapes.Moveable, public, protected, 1, mp.bridge.AvatarImpl, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.VShape, null ), (mp.shapes.Moveable, public, public, 0, main.Assignment2, null )]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\BridgeScene.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void, default scroll:mp.bridge.Avatar;int;int->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\BridgeScene.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:15 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:6 Public Methods Fraction:0.6 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.4 Private  Methods Fraction:0.0 Average Method Access:0.8 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:10 Public Properties Fraction:0.5 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.5 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:1.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\shapes\PolarPointInterface.java,tags,mp.shapes.PolarPointInterface,@Comp301Tags.LOCATABLE
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\AvatarImpl.java,accessModifiersUsed,Access Modifiers Used: [(mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null )]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\AvatarImpl.java,methods,NonGetterFunctions:[]NonSetterProcedures:[private  layoutAtOrigin:->void, public  move:int;int->void, public  scroll:int;int->void]Getters:[public  getHead:->mp.bridge.ImageShape, public  getStringShape:->mp.bridge.StringShape, public  getArms:->mp.bridge.Angle, public  getLegs:->mp.bridge.Angle]Setters:[]
5,Wed Jul 09 16:13:47 EDT 2025,true,mp\bridge\AvatarImpl.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:7 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:1 Public Methods Fraction:0.8571428571428571 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.14285714285714285 Average Method Access:0.42857142857142855 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:6.5 Average Local References per Variable:6.5 Average Local Assignments per Variable:6.5
7,Wed Jul 09 16:16:45 EDT 2025,true,main\BridgeSceneImpl.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void, public  scroll:mp.bridge.Avatar;int;int->void]Getters:[public  getArthur:->mp.bridge.Avatar, public  getLancelot:->mp.bridge.Avatar, public  getRobin:->mp.bridge.Avatar, public  getGalahad:->mp.bridge.Avatar, public  getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]
7,Wed Jul 09 16:16:45 EDT 2025,true,main\BridgeSceneImpl.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:15 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:25 Public Variables Fraction:0.24 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.04 Private  Variable Fraction:0.72 Average Variable Access:2.24 Number of Properties:10 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.7058823529411766 Average Local References per Variable:3.16 Average Local Assignments per Variable:3.16
8,Wed Jul 09 16:25:54 EDT 2025,true,mp\shapes\Moveable.java,accessModifiersUsed,Access Modifiers Used: []
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,variables,Variables:[]
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,accessModifiersUsed,Access Modifiers Used: []
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,properties,Properties:[readonly  p-v:5 access:public KnightTurn:boolean(public , null), readonly  p-v:5 access:public Gorge:mp.shapes.Gorge(public , null), readonly  p-v:5 access:public Occupied:boolean(public , null), readonly  p-v:5 access:package Arthur:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public KnightArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:5 access:package Guard:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Lancelot:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public GuardArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:5 access:package Galahad:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Robin:mp.bridge.Avatar(default , null)]
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:14 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:5 Public Methods Fraction:0.6428571428571429 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.35714285714285715 Private  Methods Fraction:0.0 Average Method Access:0.7142857142857143 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:10 Public Properties Fraction:0.5 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.5 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:1.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,innerTypes,[]
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,superTypes,[]
14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,tags,mp.bridge.BridgeScene,@Comp301Tags.BRIDGE_SCENE

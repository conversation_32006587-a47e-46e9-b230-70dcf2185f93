<Events startTimestamp="1752100758614" logVersion="1.0.0.202503121800">
  <Command __id="10" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 18:44:47 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752100758614" timestamp="328733" type="Run" />
  <Command __id="11" _type="ConsoleOutput" date="Wed Jul 09 18:44:48 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="329847" type="ConsoleOutput">
    <outputString><![CDATA[Could not load class:.\src\mp\bridge\Scrollable.java ClassNotFoundException mp.bridge.Scrollable
]]></outputString>
    <diff><![CDATA[null]]></diff>
  </Command>
  <Command __id="12" _type="ShellCommand" date="Wed Jul 09 18:44:51 EDT 2025" starttimestamp="1752100758614" timestamp="333206" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="13" _type="ShellCommand" date="Wed Jul 09 18:44:58 EDT 2025" starttimestamp="1752100758614" timestamp="339859" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="14" _type="ShellCommand" date="Wed Jul 09 18:45:01 EDT 2025" starttimestamp="1752100758614" timestamp="343155" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="15" _type="ConsoleOutput" date="Wed Jul 09 18:45:12 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="353426" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite BridgeSceneSemantics
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Could not load class:.\src\mp\bridge\Scrollable.java ClassNotFoundException mp.b"), Diff(INSERT,">>Running suite B"), Diff(EQUAL,"ridge"), Diff(DELETE,"."), Diff(EQUAL,"Sc"), Diff(DELETE,"rollable"), Diff(INSERT,"eneSemantics
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="16" _type="ConsoleOutput" date="Wed Jul 09 18:45:12 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="353452" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 18:45:12 EDT 2025<<
>>Running test BridgeSceneApproachMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>Wed Jul 09 18:45:12 EDT 2025<<
¶"), Diff(EQUAL,">>Running "), Diff(DELETE,"sui"), Diff(EQUAL,"te"), Diff(INSERT,"st"), Diff(EQUAL," BridgeScene"), Diff(DELETE,"Semantics"), Diff(INSERT,"ApproachMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="17" _type="ConsoleOutput" date="Wed Jul 09 18:45:12 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="353460" type="ConsoleOutput">
    <outputString><![CDATA[>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 18:45:12 EDT 2025<<
¶>>Running test BridgeSceneApproachMethodDefined
¶"), Diff(INSERT,"Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="18" _type="ConsoleOutput" date="Wed Jul 09 18:45:12 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="353505" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="19" _type="ConsoleOutput" date="Wed Jul 09 18:45:14 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="356173" type="ConsoleOutput">
    <outputString><![CDATA[com.puppycrawl.tools.checkstyle.api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/"), Diff(INSERT,"com.puppycrawl.tools.checkstyle.api.CheckstyleException: Exception was thrown while processing "), Diff(EQUAL,"C:"), Diff(DELETE,"/"), Diff(INSERT,"\"), Diff(EQUAL,"Users"), Diff(DELETE,"/"), Diff(INSERT,"\"), Diff(EQUAL,"14433"), Diff(DELETE,"/"), Diff(INSERT,"\"), Diff(EQUAL,"code"), Diff(DELETE,"/"), Diff(INSERT,"\"), Diff(EQUAL,"Java"), Diff(DELETE,"/"), Diff(INSERT,"\"), Diff(EQUAL,"Isa"), Diff(DELETE,"/Comp301All.jar)
¶WARNING: Please consider reporting this to the "), Diff(INSERT,"\Assn2\.\src\"), Diff(EQUAL,"main"), Diff(DELETE,"tainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,"\StaticFactoryClass.java"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="20" _type="ConsoleOutput" date="Wed Jul 09 18:45:14 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="356237" type="ConsoleOutput">
    <outputString><![CDATA[	at com.puppycrawl.tools.checkstyle.Checker.processFiles(Checker.java:311)
	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,"	at "), Diff(EQUAL,"com.puppycrawl.tools.checkstyle."), Diff(DELETE,"api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java"), Diff(INSERT,"Checker.processFiles(Checker.java:311)
¶	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
¶	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
¶	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
¶	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
¶	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
¶	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
¶	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
¶	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
¶	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
¶	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
¶	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="21" _type="ExceptionCommand" date="Wed Jul 09 18:45:15 EDT 2025" starttimestamp="1752100758614" timestamp="357045" type="Exception">
    <exceptionString><![CDATA[com.puppycrawl.tools.checkstyle.api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java
	at com.puppycrawl.tools.checkstyle.Checker.processFiles(Checker.java:311)
	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="22" _type="ExceptionCommand" date="Wed Jul 09 18:45:15 EDT 2025" starttimestamp="1752100758614" timestamp="357072" type="Exception">
    <exceptionString><![CDATA[	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948)
	at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4584)
	at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
Caused by: com.puppycrawl.tools.checkstyle.api.CheckstyleException: IllegalStateException occurred while parsing file C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java.
	at com.puppycrawl.tools.checkstyle.JavaParser.parse(JavaParser.java:106)
	at unc.tools.checkstyle.AnExtendibleTreeWalker.processFiltered(AnExtendibleTreeWalker.java:155)
	at com.puppycrawl.tools.checkstyle.api.AbstractFileSetCheck.process(AbstractFileSetCheck.java:87)
	at com.puppycrawl.tools.checkstyle.Checker.processFile(Checker.java:337)
	at com.puppycrawl.tools.checkstyle.Checker.processFiles(Checker.java:298)
	... 85 more
Caused by: java.lang.IllegalStateException: C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:7:7: unexpected token: Comp301Tags
	at com.puppycrawl.tools.checkstyle.JavaParser$1.reportError(JavaParser.java:94)
	at com.puppycrawl.tools.checkstyle.grammar.GeneratedJavaRecognizer.typeDefinition(GeneratedJavaRecognizer.java:411)
	at com.puppycrawl.tools.checkstyle.grammar.GeneratedJavaRecognizer.compilationUnit(GeneratedJavaRecognizer.java:202)
	at com.puppycrawl.tools.checkstyle.JavaParser.parse(JavaParser.java:100)
	... 89 more
Caused by: C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:7:7: unexpected token: Comp301Tags
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="23" _type="ConsoleOutput" date="Wed Jul 09 18:45:16 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="357861" type="ConsoleOutput">
    <outputString><![CDATA[com.puppycrawl.tools.checkstyle.api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at "), Diff(EQUAL,"com.puppycrawl.tools.checkstyle."), Diff(DELETE,"Checker.processFiles(Checker.java:311)
¶	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
¶	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
¶	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
¶	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
¶	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
¶	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
¶	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
¶	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
¶	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
¶	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
¶	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(INSERT,"api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="24" _type="ConsoleOutput" date="Wed Jul 09 18:45:16 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="357916" type="ConsoleOutput">
    <outputString><![CDATA[	at com.puppycrawl.tools.checkstyle.Checker.processFiles(Checker.java:311)
	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,"	at "), Diff(EQUAL,"com.puppycrawl.tools.checkstyle."), Diff(DELETE,"api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java"), Diff(INSERT,"Checker.processFiles(Checker.java:311)
¶	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
¶	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
¶	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
¶	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
¶	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
¶	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
¶	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
¶	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
¶	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
¶	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
¶	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="25" _type="ConsoleOutput" date="Wed Jul 09 18:45:17 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="358464" type="ConsoleOutput">
    <outputString><![CDATA[com.puppycrawl.tools.checkstyle.api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at "), Diff(EQUAL,"com.puppycrawl.tools.checkstyle."), Diff(DELETE,"Checker.processFiles(Checker.java:311)
¶	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
¶	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
¶	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
¶	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
¶	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
¶	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
¶	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
¶	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
¶	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
¶	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
¶	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(INSERT,"api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="26" _type="ConsoleOutput" date="Wed Jul 09 18:45:17 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="358492" type="ConsoleOutput">
    <outputString><![CDATA[	at com.puppycrawl.tools.checkstyle.Checker.processFiles(Checker.java:311)
	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,"	at "), Diff(EQUAL,"com.puppycrawl.tools.checkstyle."), Diff(DELETE,"api.CheckstyleException: Exception was thrown while processing C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java"), Diff(INSERT,"Checker.processFiles(Checker.java:311)
¶	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
¶	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
¶	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
¶	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
¶	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
¶	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
¶	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
¶	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
¶	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
¶	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
¶	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="27" _type="ShellCommand" date="Wed Jul 09 18:45:36 EDT 2025" starttimestamp="1752100758614" timestamp="377452" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="28" _type="ShellCommand" date="Wed Jul 09 18:46:40 EDT 2025" starttimestamp="1752100758614" timestamp="441490" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="29" _type="ShellCommand" date="Wed Jul 09 18:48:25 EDT 2025" starttimestamp="1752100758614" timestamp="546732" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="30" _type="ShellCommand" date="Wed Jul 09 18:49:05 EDT 2025" starttimestamp="1752100758614" timestamp="587038" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="31" _type="ShellCommand" date="Wed Jul 09 18:49:19 EDT 2025" starttimestamp="1752100758614" timestamp="601164" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="32" _type="ShellCommand" date="Wed Jul 09 18:49:22 EDT 2025" starttimestamp="1752100758614" timestamp="603480" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="33" _type="ShellCommand" date="Wed Jul 09 18:49:23 EDT 2025" starttimestamp="1752100758614" timestamp="605285" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="34" _type="ShellCommand" date="Wed Jul 09 18:49:29 EDT 2025" starttimestamp="1752100758614" timestamp="610549" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="35" _type="ExceptionCommand" date="Wed Jul 09 18:59:44 EDT 2025" starttimestamp="1752100758614" timestamp="1225419" type="Exception">
    <exceptionString><![CDATA[java.lang.SecurityException
	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
	at java.base/java.lang.Runtime.exit(Runtime.java:113)
	at java.base/java.lang.System.exit(System.java:1860)
	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="36" _type="ExceptionCommand" date="Wed Jul 09 18:59:47 EDT 2025" starttimestamp="1752100758614" timestamp="1229370" type="Exception">
    <exceptionString><![CDATA[Exception in thread "AWT-EventQueue-0" java.lang.SecurityException
	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
	at java.base/java.lang.Runtime.exit(Runtime.java:113)
	at java.base/java.lang.System.exit(System.java:1860)
	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="37" _type="ConsoleOutput" date="Wed Jul 09 18:59:48 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="1229977" type="ConsoleOutput">
    <outputString><![CDATA[Exception in thread "AWT-EventQueue-0" java.lang.SecurityException
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at com.puppycrawl.tools.checkstyle.Checker.processFiles(Checker.java:311)
¶	at com.puppycrawl.tools.checkstyle.Checker.process(Checker.java:221)
¶	at unc.tools.checkstyle.AnExtendibleChecker.process(AnExtendibleChecker.java:19)
¶	at com.puppycrawl.tools.checkstyle.Main.runCheckstyle(Main.java:408)
¶	at com.puppycrawl.tools.checkstyle.Main.runCli(Main.java:331)
¶	at com.puppycrawl.tools.checkstyle.Main.execute(Main.java:190)
¶	at com.puppycrawl.tools.checkstyle.Main.main(Main.java:125)
¶	at unc.tools.checkstyle.NonExitingMain.main(NonExitingMain.java:19)
¶	at unc.tools.checkstyle.PostProcessingMain.main(PostProcessingMain.java:1817)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:93)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:29)
¶	at grader.basics.checkstyle.CheckStyleInvoker.runCheckstyle(CheckStyleInvoker.java:100)
¶	at grader.basics.project.BasicProject.getCheckstyleText(BasicProject.java:1074)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckStyleTestCase.test(CheckStyleTestCase.java:370)
¶	at gradingTools.basics.sharedTestCase.checkstyle.CheckstyleMethodDefinedTestCase.test(CheckstyleMethodDefinedTestCase.java:70)
¶	at grader.basics.testcase.PassFailJUnitTestCase.passfailDefaultTest(PassFailJUnitTestCase.java:277)
¶	at grader.basics.testcase.PassFailJUnitTestCase.defaultTest(PassFailJUnitTestCase.java:134)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(INSERT,"Exception in thread "AWT-EventQueue-0" java.lang.SecurityException"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="38" _type="ConsoleOutput" date="Wed Jul 09 18:59:48 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="1229996" type="ConsoleOutput">
    <outputString><![CDATA[	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
	at java.base/java.lang.Runtime.exit(Runtime.java:113)
	at java.base/java.lang.System.exit(System.java:1860)
	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Exception in thread "AWT-EventQueue-0" java.lang.SecurityException"), Diff(INSERT,"	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
¶	at java.base/java.lang.Runtime.exit(Runtime.java:113)
¶	at java.base/java.lang.System.exit(System.java:1860)
¶	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
¶	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
¶	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
¶	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
¶	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
¶	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
¶	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="39" _type="ConsoleOutput" date="Wed Jul 09 18:59:49 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="1230548" type="ConsoleOutput">
    <outputString><![CDATA[Exception in thread "AWT-EventQueue-0" java.lang.SecurityException
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
¶	at java.base/java.lang.Runtime.exit(Runtime.java:113)
¶	at java.base/java.lang.System.exit(System.java:1860)
¶	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
¶	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
¶	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
¶	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
¶	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
¶	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
¶	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)"), Diff(INSERT,"Exception in thread "AWT-EventQueue-0" java.lang.SecurityException"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="40" _type="ConsoleOutput" date="Wed Jul 09 18:59:49 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="1230562" type="ConsoleOutput">
    <outputString><![CDATA[	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
	at java.base/java.lang.Runtime.exit(Runtime.java:113)
	at java.base/java.lang.System.exit(System.java:1860)
	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Exception in thread "AWT-EventQueue-0" java.lang.SecurityException"), Diff(INSERT,"	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
¶	at java.base/java.lang.Runtime.exit(Runtime.java:113)
¶	at java.base/java.lang.System.exit(System.java:1860)
¶	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
¶	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
¶	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
¶	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
¶	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
¶	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
¶	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="41" _type="ExceptionCommand" date="Wed Jul 09 18:59:49 EDT 2025" starttimestamp="1752100758614" timestamp="1230909" type="Exception">
    <exceptionString><![CDATA[Exception in thread "AWT-EventQueue-0" java.lang.SecurityException
	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
	at java.base/java.lang.Runtime.exit(Runtime.java:113)
	at java.base/java.lang.System.exit(System.java:1860)
	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="42" _type="ConsoleOutput" date="Wed Jul 09 18:59:49 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="1231277" type="ConsoleOutput">
    <outputString><![CDATA[Exception in thread "AWT-EventQueue-0" java.lang.SecurityException
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
¶	at java.base/java.lang.Runtime.exit(Runtime.java:113)
¶	at java.base/java.lang.System.exit(System.java:1860)
¶	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
¶	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
¶	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
¶	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
¶	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
¶	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
¶	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)"), Diff(INSERT,"Exception in thread "AWT-EventQueue-0" java.lang.SecurityException"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="43" _type="ConsoleOutput" date="Wed Jul 09 18:59:49 EDT 2025" overflow="false" starttimestamp="1752100758614" timestamp="1231291" type="ConsoleOutput">
    <outputString><![CDATA[	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
	at java.base/java.lang.Runtime.exit(Runtime.java:113)
	at java.base/java.lang.System.exit(System.java:1860)
	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Exception in thread "AWT-EventQueue-0" java.lang.SecurityException"), Diff(INSERT,"	at unc.tools.checkstyle.NonExitingSecurityManager.checkExit(NonExitingSecurityManager.java:8)
¶	at java.base/java.lang.Runtime.exit(Runtime.java:113)
¶	at java.base/java.lang.System.exit(System.java:1860)
¶	at grader.basics.observers.logSending.ALogSendingRunnable.windowClosing(ALogSendingRunnable.java:65)
¶	at java.desktop/java.awt.AWTEventMulticaster.windowClosing(AWTEventMulticaster.java:358)
¶	at java.desktop/java.awt.Window.processWindowEvent(Window.java:2085)
¶	at java.desktop/javax.swing.JFrame.processWindowEvent(JFrame.java:298)
¶	at java.desktop/java.awt.Window.processEvent(Window.java:2044)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
¶	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
¶	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="45" _type="ShellCommand" date="Wed Jul 09 18:59:54 EDT 2025" starttimestamp="1752100758614" timestamp="1236338" type="ECLIPSE_CLOSED" />
</Events>

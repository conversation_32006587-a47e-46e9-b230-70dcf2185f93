
//SESSION START
0,<PERSON><PERSON> Jul 08 19:17:59 EDT 2025,1033
//START OF FILE: main/RunSS25A2Tests.java
package main;

import grader.basics.execution.BasicProjectExecution;
import gradingTools.comp301ss24.assignment0.SS24Assignment0Suite;
import gradingTools.comp301ss24.assignment2.SS24Assignment2Suite;
import trace.grader.basics.GraderBasicsTraceUtility;
public class RunSS25A2Tests {
	public static void main(String[] args) {
		// if you set this to false, grader steps will not be traced
		GraderBasicsTraceUtility.setTracerShowInfo(true);
		// if you set this to false, all grader steps will be traced,
		// not just the ones that failed
		GraderBasicsTraceUtility.setBufferTracedMessages(true);
		// Change this number if a test trace gets longer than 600 and is clipped
		GraderBasicsTraceUtility.setMaxPrintedTraces(600);
		// Change this number if all traces together are longer than 2000
		GraderBasicsTraceUtility.setMaxTraces(2000);
		// Change this number if your process times out prematurely
		BasicProjectExecution.setProcessTimeOut(5);
		// You need to always call such a method
		SS24Assignment2Suite.main(args);
	}
}

//END OF FILE

//SESSION END

//SESSION START
1,Tue Jul 08 20:25:34 EDT 2025,17906
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class Gorge {
    private static final int LEFT_LINE_X = 750;
    private static final int RIGHT_LINE_X = 950;
    private static final int LINE_TOP_Y = 0;
    private static final int LINE_HEIGHT = 1000;
    private static final int BRIDGE_LENGTH = RIGHT_LINE_X - LEFT_LINE_X;
    private static final int c1 = 0;
    private static final int c2 = -1000;
    private static final int c3 = -200;
    private static final int c4 = 400;
    private static final int c5 = 500;
    private RotatingLine leftLine;
    private RotatingLine rightLine;
    private RotatingLine bridge;
    private RotatingLine bridge2;

    public Gorge() {
        leftLine = new RotatingLine();
        leftLine.setRadius(LINE_HEIGHT);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(LEFT_LINE_X);
        leftLine.setY(LINE_TOP_Y);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(LINE_HEIGHT);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(RIGHT_LINE_X);
        rightLine.setY(LINE_TOP_Y);
        rightLine.move(c1, c2);
        
        bridge = new RotatingLine();
        bridge.setRadius(BRIDGE_LENGTH);
        bridge.setAngle(0);
        bridge.setX(LEFT_LINE_X);
        bridge.setY(LINE_TOP_Y);
        bridge.move(c3, c4);
        
        bridge2 = new RotatingLine();
        bridge2.setRadius(BRIDGE_LENGTH);
        bridge2.setAngle(0);
        bridge2.setX(LEFT_LINE_X);
        bridge2.setY(LINE_TOP_Y);
        bridge2.move(c3, c5);
    }

    public RotatingLine getLeftLine() {
    	return leftLine;
    }
    public RotatingLine getRightLine(){
    	return rightLine;
    }
    public RotatingLine getBridge(){
    	return bridge; 
    }
    public RotatingLine getBridge2() {
    	return bridge2;
    }
}
//END OF FILE
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    public static void main(String[] args) {
      ObjectEditor.edit(new LancelotHead());
    }
}

//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import bus.uigen.ObjectEditor;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    public Gorge gorge;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(some_x*guard_const,some_y);
      this.gorge = new Gorge();
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
}

//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
}
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape {
    private String text = "Hi";
    private int x, y;
    public SpeechBubble() {
    	
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return x;
    }
    @Override
    public void setX(int x) { 
    	this.x = x; }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    
    public static void main(String[] args) {
      ObjectEditor.edit(new SpeechBubble());
    }
}

//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

public class APolarPoint implements Point {
	double radius, angle;
	public APolarPoint(double theRadius, double theAngle) {
		radius = theRadius;
		angle = theAngle;
	}
	public APolarPoint(int theX, int theY) {
		radius = Math.sqrt(theX*theX + theY*theY);
		angle = Math.atan((double) theY/theX);
	}
	@Override
	public int getX() { return (int) (radius*Math.cos(angle)); }
	@Override
	public int getY() { return (int) (radius*Math.sin(angle)); }
	@Override
	public double getAngle() { return angle; } 
	@Override
	public double getRadius() { return radius;}	
}
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    public static void main(String[] args) {
      ObjectEditor.edit(new GuardHead());
    }
}

//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;   

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int dx, int dy);
}

//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags({ Comp301Tags.ROTATING_LINE })
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine {
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
    int getWidth();
    int getHeight();
    double getRadius();
    void setRadius(double r);
    double getAngle();
    void setAngle(double angle);
    void rotate(int units);
    void move(int x, int y);
}
//END OF FILE
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape {
    String getText();
    void setText(String t);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    public static void main(String[] args) {
      ObjectEditor.edit(new ArthurHead());
    }
}

//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    public static void main(String[] args) {
      ObjectEditor.edit(new GalahadHead());
    }
}

//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;

import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
      }
    public static void main(String[] args) {
      ObjectEditor.edit(new VShape());
    }
}

//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

//END OF FILE
//START OF FILE: mp/shapes/Point.java
package mp.shapes;
import util.annotations.Explanation;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.POINT_PATTERN)
@Explanation("Location in Java coordinate System.")
public interface Point {
	public int getX(); 
	public int getY(); 	
	public double getAngle(); 
	public double getRadius(); 
}

//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head   = head;
        this.speech = new SpeechBubble(); 
        this.arms   = new VShape();      
        this.legs   = new VShape();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }

    @Override
    public void move(int dx, int dy) {
        // move the head
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }

    public static void main(String[] args) {
        ObjectEditor.edit(new AvatarImpl(new ArthurHead()));
    }
}
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags({ Comp301Tags.ROTATING_LINE })
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;

    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }

    @Override public int getX() {
        return a + point.getX();
    }
    @Override public void setX(int x) {
        this.a = x;
    }

    @Override public int getY() {
        return b + point.getY();
    }
    @Override public void setY(int y) {
        this.b = y;
    }

    @Override public int getWidth() {
        return point.getX();
    }

    @Override public int getHeight() {
        return point.getY();
    }

    @Override public double getRadius() {
        return point.getRadius();
    }
    @Override public void setRadius(double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    @Override public double getAngle() {
        return point.getAngle();
    }
    @Override public void setAngle(double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
      }
}

//END OF FILE
//START OF FILE: main/Assignment1.java
package main;

import bus.uigen.OEFrame;
import bus.uigen.ObjectEditor;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;

public class Assignment1 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotateLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }

    public static void main(String[] args) throws InterruptedException {
        ObjectEditor.edit(new BridgeSceneImpl());
    }
}

//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape {
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    public static void main(String[] args) {
      ObjectEditor.edit(new RobinHead());
    }
}

//END OF FILE

//SESSION END

//SESSION START
2,Tue Jul 08 22:18:48 EDT 2025,64
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class Gorge {
    int RIGHT_LINE_X = 950;
    int LINE_TOP_Y = 0;
    int LINE_HEIGHT = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotatingLine leftLine;
    RotatingLine rightLine;
    AScalableRectangle rectangle;
    public Gorge(int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(LINE_HEIGHT);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(LINE_TOP_Y);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(LINE_HEIGHT);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(RIGHT_LINE_X);
        rightLine.setY(LINE_TOP_Y);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, RIGHT_LINE_X - x, lower);
    }

    public RotatingLine getLeftLine() {
    	return leftLine;
    }
    public RotatingLine getRightLine(){
    	return rightLine;
    }
    public AScalableRectangle getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=281	-69	=28	-21	=24	-21	=28	-94	=16	-21	=20	-21	=4	-15	+up	=1	-5	=1	-20	+r	=12	-2	=1	-2	+n	=1	-1	=1	-11	=1	-7	+ower	=3	-1	+1	=8	-8	=27	-8	=28	-4	+ASc	=1	-1	+labl	=1	-1	=1	-1	+ec	=2	-2	=2	-3	+l	=2	-1	=1	-3	=1	-16	+c	=2	-2	=2	-10	+l	=1	-1	=1	-1	=18	+int x	=146	-11	+x	=322	-1	=1	-2	+ectan	=1	+l	=8	-3	+ASc	=1	-31	+l	=1	-29	=1	-4	+l	=1	-2	+R	=1	+c	=1	-1	+a	=5	-6	+x,	=1	-10	+upp	=1	-29	=1	-45	=2	-79	=2	-1	=1	-7	=1	-56	=8	-5	=1	-3	+-	=1	+x,	=1	-5	+low	=1	-29	=1	-17	=158	+AScalable	=1	-1	+ec	=2	-2	=2	-3	+l	=5	-5	+R	=1	-40	=1	-3	=2	-2	=2	-13	+l	=1	-1	=17	-1	=1	-2	+ectan	=1	+l	=1	-1	=9
//END OF FILE
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;


import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=20	-30	=800	-96	=9
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=27	-31	=786	-94	=9
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;


import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=20	-30	=797	-95	=9
//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private AvatarImpl cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(750);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void approach(AvatarImpl avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
}

(DIFF_FROM_PREVIOUS_FILE)
=21	-31	=22	+	=372	+import mp.shapes.AScalableRectangle;	=1	-0	=474	-3	+r	=1	-1	+vate	=14	+    private AvatarImpl cur;%0A    private AScalableRectangle knightArea;%0A    private AScalableRectangle guardArea;%0A    private boolean KnightTurn = false;%0A    private static final int AREA_X = 500;%0A    private static final int KNIGHT_Y = 600; %0A    private static final int GUARD_Y = 350;%0A    private static final int AREA_WIDTH = 120;%0A    private static final int AREA_HEIGHT = 100;%0A    private boolean Occupied = false;%0A	=470	-1	+AREA_X,GUARD_Y);%0A      g	=1	-1	+rge = new Gorge(750);%0A      knightArea = new AScalableR	=1	+ctangle(AREA	=1	-2	+X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);%0A      	=5	-3	+Area = 	=1	-1	+ew AScalableRec	=1	+angle(AREA_X	=1	-1	+GUARD_Y,AREA_WIDTH,AREA_HEIGHT);%0A    %7D%0A    public v	=1	+id approach(AvatarI	=1	+pl avatar)%7B%0A    %09if(!Occupi	=1	+d) %7Bavatar.move(AREA	=1	-1	+X, KNIGHT_Y	=2	+%7D	=5	+%09Occupied	=1	+=	=2	-1	+rue;%0A    %09cur = avatar;%0A    %7D%0A    public vo	=1	+d 	=1	+ay(String s)%7B%0A    %09if(Occupied)%7B%0A    %09%09if(!KnightTurn)%7Bguard	=2	-1	+etSt	=1	+in	=1	+Shap	=1	-1	+().setText(s);KnightTurn	=2	+!K	=1	+ightTurn;%7D %0A    %09%09	=1	-1	+lse	=1	-2	+%7Bcu	=1	+.	=2	+tStringShape	=2	+.setText(s);KnightTurn = !KnightTurn;%7D%0A    %09%7D else %7Breturn;%7D%0A    %09return	=369	-0	=4	+%7D%0A    public AScalableRectangle getKnightArea() %7B%0A        return knightArea;%0A    %7D%0A    public AScalableRectangle getGuardArea() %7B%0A        return guardArea;%0A    %7D%0A    public Gorge getGorge() %7Breturn gorge;	=4
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import bus.uigen.ObjectEditor;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape {
    private String text = "Hi";
    private int x, y;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return x;
    }
    @Override
    public void setX(int x) { 
    	this.x = x; }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=334	-6	=392	-101	=9
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;

import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
      }
}

(DIFF_FROM_PREVIOUS_FILE)
=87	-31	=666	-90	=8
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=27	-31	=785	-93	=9
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head   = head;
        this.speech = new SpeechBubble(); 
        this.arms   = new VShape();      
        this.legs   = new VShape();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=19	-32	=962	-1	=52	-25	=236	-113	=10
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags({ Comp301Tags.ROTATING_LINE })
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;

    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }

    @Override public int getX() {
        return a + point.getX();
    }
    @Override public void setX(int x) {
        this.a = x;
    }

    @Override public int getY() {
        return b + point.getY();
    }
    @Override public void setY(int y) {
        this.b = y;
    }

    @Override public int getWidth() {
        return point.getX();
    }

    @Override public int getHeight() {
        return point.getY();
    }

    @Override public double getRadius() {
        return point.getRadius();
    }
    @Override public void setRadius(double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    @Override public double getAngle() {
        return point.getAngle();
    }
    @Override public void setAngle(double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=1546	-2	=4
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;

import util.annotations.StructurePattern;

@StructurePattern("Rectangle Pattern")
public class AScalableRectangle{
	int x, y, width, height;
	public AScalableRectangle(int theX, int theY, int theWidth, int theHeight) {
		x = theX;
		y = theY;
		width = theWidth;
		height = theHeight;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/100;
		height = (height*percentage)/100;		
	}
}
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=27	-31	=783	-93	=9
//END OF FILE

//SESSION END

//SESSION START
6,Tue Jul 08 22:22:22 EDT 2025,-66
//START OF FILE: main/RunSS25A2Tests.java
package main;

import grader.basics.execution.BasicProjectExecution;
import gradingTools.comp301ss24.assignment2.SS24Assignment2Suite;
import trace.grader.basics.GraderBasicsTraceUtility;
public class RunSS25A2Tests {
	public static void main(String[] args) {
		// if you set this to false, grader steps will not be traced
		GraderBasicsTraceUtility.setTracerShowInfo(true);
		// if you set this to false, all grader steps will be traced,
		// not just the ones that failed
		GraderBasicsTraceUtility.setBufferTracedMessages(true);
		// Change this number if a test trace gets longer than 600 and is clipped
		GraderBasicsTraceUtility.setMaxPrintedTraces(600);
		// Change this number if all traces together are longer than 2000
		GraderBasicsTraceUtility.setMaxTraces(2000);
		// Change this number if your process times out prematurely
		BasicProjectExecution.setProcessTimeOut(5);
		// You need to always call such a method
		SS24Assignment2Suite.main(args);
	}
}

(DIFF_FROM_PREVIOUS_FILE)
=111	-66	=856
//END OF FILE

//SESSION END

//SESSION START
14,Tue Jul 08 23:04:20 EDT 2025,507
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private AvatarImpl cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(AvatarImpl avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
}

(DIFF_FROM_PREVIOUS_FILE)
=1370	+rivate static final int Gorge_X = 750;%0A    private static int Gorge_Y = 0;%0A    int diff = 50;%0A    p	=26	-0	=480	-3	+Gorge_X	=86	-0	=80	+%0A    %7D%0A    public void passed()%7B%0A    %09if(!KnightTurn)%7B%0A    %09%09cur.move(AREA_X, KNIGHT_Y);%0A    %09%09Occupied = false;%0A    %09%7D%0A    %7D%0A    public void failed()%7B%0A    %09if(Occupied) %7B%0A    %09%09if(!KnightTurn) %7B%0A    %09cur.getHead().setX(Gorge_X);%0A    %09cur.getHead().setY(Gorge_Y);%0A    %09Gorge_Y += diff;%7D%0A    %09%09else %7Bguard.getHead().setX(Gorge_X);%0A        %09guard.getHead().setY(Gorge_Y);%0A        %09Gorge_Y += diff;%7D%0A    %09%09%7D	=969
//END OF FILE

//SESSION END

//SESSION START
15,Tue Jul 08 23:06:59 EDT 2025,19
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
}

(DIFF_FROM_PREVIOUS_FILE)
=964	-4	=1465	+%0A    %09Occupied = !Occupied;	=157	-4	=927
//END OF FILE

//SESSION END

//SESSION START
16,Tue Jul 08 23:08:51 EDT 2025,52
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
}

(DIFF_FROM_PREVIOUS_FILE)
=3538	+    public boolean getOccupied() %7Breturn Occupied;%7D%0A	=2
//END OF FILE

//SESSION END

//SESSION START
17,Tue Jul 08 23:09:41 EDT 2025,56
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=3590	+    public boolean getKnightTurn() %7Breturn KnightTurn;%7D%0A	=2
//END OF FILE

//SESSION END

//SESSION START
18,Tue Jul 08 23:10:43 EDT 2025,-28
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape {
    private String text = "Grail";
    private int x, y;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return x;
    }
    @Override
    public void setX(int x) { 
    	this.x = x; }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=27	-31	=217	-1	+Gra	=1	+l	=458
//END OF FILE

//SESSION END

//SESSION START
21,Tue Jul 08 23:33:23 EDT 2025,-236
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge {
    int RIGHT_LINE_X = 950;
    int LINE_TOP_Y = 0;
    int LINE_HEIGHT = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotatingLine leftLine;
    RotatingLine rightLine;
    AScalableRectangle rectangle;
    public Gorge(int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(LINE_HEIGHT);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(LINE_TOP_Y);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(LINE_HEIGHT);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(RIGHT_LINE_X);
        rightLine.setY(LINE_TOP_Y);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, RIGHT_LINE_X - x, lower);
    }

    public RotatingLine getLeftLine() {
    	return leftLine;
    }
    public RotatingLine getRightLine(){
    	return rightLine;
    }
    public AScalableRectangle getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=19	-236	=1052
//END OF FILE

//SESSION END

//SESSION START
22,Wed Jul 09 00:02:11 EDT 2025,-10
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;

    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }

    @Override public int getX() {
        return a + point.getX();
    }
    @Override public void setX(int x) {
        this.a = x;
    }

    @Override public int getY() {
        return b + point.getY();
    }
    @Override public void setY(int y) {
        this.b = y;
    }

    @Override public int getWidth() {
        return point.getX();
    }

    @Override public int getHeight() {
        return point.getY();
    }
    
    @Override public double getRadius() {
        return point.getRadius();
    }
    @Override public void setRadius(double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    @Override public double getAngle() {
        return point.getAngle();
    }
    @Override public void setAngle(double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=166	-1	=7	-1	=12	-1	+L	=1	-1	+C	=2	-4	+AB	=1	-2	=1	-1	=755	+    	=594
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine {
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
    int getWidth();
    int getHeight();
    double getRadius();
    void setRadius(double r);
    double getAngle();
    void setAngle(double angle);
    void rotate(int units);
    void move(int x, int y);
}
(DIFF_FROM_PREVIOUS_FILE)
=167	-1	=7	-1	=12	-1	+L	=1	-1	+C	=2	-4	+AB	=1	-2	=1	-1	=372
//END OF FILE

//SESSION END

//SESSION START
23,Wed Jul 09 00:17:17 EDT 2025,426
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.Angle;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}

public static Angle legsFactoryMethod()
{
	   return new VShape (t1, tN);		
}
}
//END OF FILE

//SESSION END

//SESSION START
24,Wed Jul 09 00:24:38 EDT 2025,-194
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=31	-211	+*	=3406
//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.Angle;
import mp.bridge.VShape;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}

public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=55	+VShape;%0Aimport mp.bridge.	=355	-1	=1	-6	=8
//END OF FILE
//START OF FILE: main/Assignment1.java
package main;

import bus.uigen.*;
import mp.shapes.*;
import mp.bridge.*;
public class Assignment1 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotateLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }

    public static void main(String[] args) throws InterruptedException {
    	BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=32	-38	+*	=19	-10	+*	=12	-12	+br	=1	-1	+d	=1	-3	=1	+.*	=1	-1	=852	+%09BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();%0A    	=22	-11	+s	=4	-6	=11
//END OF FILE

//SESSION END

//SESSION START
26,Wed Jul 09 00:56:15 EDT 2025,977
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;

import mp.shapes.RotatingLine;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotatingLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotatingLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotatingLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
      }
}

(DIFF_FROM_PREVIOUS_FILE)
=42	-29	=298	-1	+ing	=144	-1	+ing	=78	-1	+ing	=167
//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}

public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-60	+*	=353
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import util.models.PropertyListenerRegisterer;
import util.annotations.Tags;
import util.annotations.Visible;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements PropertyListenerRegisterer {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }
    @Visible(false)
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return propertyChangeListeners;
    }
    public int getX() {
        return a + point.getX();
    }
    public void setX(int x) {
    	fire("X",this.a,x);
        this.a = x;
    }

    public int getY() {
        return b + point.getY();
    }
    public void setY(int y) {
    	fire("Y",this.b,y);
        this.b = y;
    }

    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(double r) {
    	fire("Radius", this.point.getRadius(), r);
        point = new APolarPoint(r, point.getAngle());
        fire("Width", null, getWidth());
        fire("Height", null, getHeight());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(double angle) {
    	fire("Angle", this.getAngle(), angle);
        point = new APolarPoint(point.getRadius(), angle);
        fire("Width", null, getWidth());
        fire("Height", null, getHeight());
    }

    public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
    }
    private void fire(String name, Object old, Object newv) {
        PropertyChangeEvent evt = new PropertyChangeEvent(this, name, old, newv);
        for (PropertyChangeListener l : propertyChangeListeners) {
            l.propertyChange(evt);
        }
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=26	+java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;%0Aimport util.models.PropertyListenerRegisterer;%0Aimport 	=21	+;%0Aimport util.annotations.Visible	=119	+import java.beans.PropertyChangeEvent;%0A	=122	-1	+Pr	=1	-3	+p	=1	+rty	=2	+ste	=2	+rRegisterer	=103	+    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();	=119	-1	=13	+%0A    public void addPropertyChangeListener(PropertyChangeListener listener) %7B%0A        propertyChangeListeners.add(listener);%0A    %7D%0A    @Visible(false)%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return propertyChangeListeners;%0A    %7D%0A   	=64	-10	=30	+%09fire(%22X%22,this.a,x);%0A    	=26	-10	=64	-10	=30	+%09fire(%22Y%22,this.b,y);%0A    	=27	-10	=64	-10	=69	-10	=72	-10	=38	+%09fire(%22Radius%22, this.point.getRadius(), r);%0A    	=54	-3	=4	-3	+fir	=1	-2	+(%22W	=2	+th%22, null, g	=1	+tWidth());%0A        fire(%22Height%22, null, getHeight());%0A    %7D%0A%0A   	=71	-10	=41	+%09fire(%22Angle%22, this.getAngle(), angle);%0A    	=59	-1	+    fire(%22Width%22, null, getWidth());	=1	-1	+    	=4	-3	+fire(%22H	=1	-2	=1	-1	+ght%22, null, g	=1	+tHeight());%0A    %7D%0A%0A   	=95	-10	=78	+%0A    %7D%0A    private void fire(String name, Object old, Object newv) %7B%0A        PropertyChangeEvent evt = new PropertyChangeEvent(this, name, old, newv);%0A        for (PropertyChangeListener l : propertyChangeListeners) %7B%0A            l.propertyChange(evt);%0A        %7D	=9
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import mp.shapes.RotatingLine;   

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle {
    RotatingLine getLeftLine();
    RotatingLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=189	-1	+ing	=123	-1	+ing	=29	-1	+ing	=54
//END OF FILE
//START OF FILE: main/Assignment1.java
package main;

import bus.uigen.*;
import mp.shapes.*;
import mp.bridge.*;
public class Assignment1 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotatingLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }

    public static void main(String[] args) throws InterruptedException {
    	BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=488	-1	+ing	=547
//END OF FILE

//SESSION END

//SESSION START
28,Wed Jul 09 00:58:35 EDT 2025,90
//START OF FILE: main/Assignment1.java
package main;

import bus.uigen.*;
import mp.shapes.*;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.BRIDGE_SCENE)
public class Assignment1 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotatingLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }

    public static void main(String[] args) throws InterruptedException {
    	BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=75	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BRIDGE_SCENE)%0A	=963
//END OF FILE

//SESSION END

//SESSION START
29,Wed Jul 09 00:59:27 EDT 2025,-120
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;

@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=242	-31	=3165
//END OF FILE
//START OF FILE: main/Assignment1.java
package main;

import bus.uigen.*;
import mp.shapes.*;
import mp.bridge.*;

public class Assignment1 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotatingLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }

    public static void main(String[] args) throws InterruptedException {
    	BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=75	-89	=964
//END OF FILE

//SESSION END

//SESSION START
30,Wed Jul 09 01:01:09 EDT 2025,359
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod() {
  return BridgeSceneImpl.getSingletonInstance();  // or new once, same singleton logic
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod() {
  return ConsoleSceneViewImpl.getSingletonInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=312	+@Tags(Comp301Tags.BRIDGE_SCENE)%0Apublic static BridgeScene bridgeSceneFactoryMethod() %7B%0A  return BridgeSceneImpl.getSingletonInstance();  // or new once, same singleton logic%0A%7D%0A@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)%0Apublic static ConsoleSceneView consoleSceneViewFactoryMethod() %7B%0A  return ConsoleSceneViewImpl.getSingletonInstance();%0A%7D%0A@Tags(Comp301Tags.ANGLE)	=73
//END OF FILE

//SESSION END

//SESSION START
31,Wed Jul 09 01:01:25 EDT 2025,-335
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=330	-335	=79
//END OF FILE

//SESSION END

//SESSION START
32,Wed Jul 09 01:02:12 EDT 2025,32
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=185	+@Tags(Comp301Tags.BRIDGE_SCENE)%0A	=224
//END OF FILE

//SESSION END

//SESSION START
33,Wed Jul 09 01:20:54 EDT 2025,2420
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;

import util.annotations.Visible;
import mp.shapes.RotatingLine;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotatingLine left, right;
    private final List<PropertyChangeListener> listeners = new ArrayList<>();
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Visible(false)
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return listeners;
    }
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotatingLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotatingLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        int oldLX = left.getX(), oldLY = left.getY();
        int oldRX = right.getX(), oldRY = right.getY();
        left.move(dx, dy);
        right.move(dx, dy);
        notify("LeftLine.X", oldLX, left.getX());
        notify("LeftLine.Y", oldLY, left.getY());
        notify("RightLine.X", oldRX, right.getX());
        notify("RightLine.Y", oldRY, right.getY());
    }
 
    private void notify(String name, Object old, Object newv) {
        PropertyChangeEvent e = new PropertyChangeEvent(this, name, old, newv);
        for (PropertyChangeListener l : listeners) {
            l.propertyChange(e);
        }
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;%0A%0Aimport util.annotations.Visible;	=342	+    private final List%3CPropertyChangeListener%3E listeners = new ArrayList%3C%3E();%0A    public void addPropertyChangeListener(PropertyChangeListener l) %7B%0A        listeners.add(l);%0A    %7D%0A    @Visible(false)%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return listeners;%0A    %7D%0A	=320	+int oldLX = left.getX(), oldLY = left.getY();%0A        int oldRX = right.getX(), oldRY = right.getY();%0A        	=49	+      notify(%22LeftLine.X%22, oldLX, left.getX());%0A        notify(%22LeftLine.Y%22, oldLY, left.getY());%0A        notify(%22RightLine.X%22, oldRX, right.getX());%0A        notify(%22RightLine.Y%22, oldRY, right.getY());%0A    %7D%0A %0A    private void notify(String name, Object old, Object newv) %7B%0A        PropertyChangeEvent e = new PropertyChangeEvent(this, name, old, newv);%0A        for (PropertyChangeListener l : listeners) %7B%0A            l.propertyChange(e);%0A        %7D%0A	=8
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar {
    ImageShape getHead();
    StringShape getStringShape();
    VShape getArms();
    VShape getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=338	-4	+VShap	=17	-4	+VShap	=46
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final VShape arms;
    private final VShape legs;

    public AvatarImpl(ImageShape head) {
        this.head   = head;
        this.speech = new SpeechBubble(); 
        this.arms   = new VShape();      
        this.legs   = new VShape();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public VShape getArms(){
    	return arms; 
    }
    @Override
    public VShape getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=382	-4	+VShap	=26	-4	+VShap	=455	-4	+VShap	=63	-4	+VShap	=337
//END OF FILE
//START OF FILE: main/ConsoleSceneView.java
package main;

import java.beans.PropertyChangeListener;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public interface ConsoleSceneView extends PropertyChangeListener {
}
//END OF FILE
//START OF FILE: main/ConsoleSceneViewIm.java
package main;

import java.beans.PropertyChangeEvent;
import java.util.Arrays;
import java.util.List;
import mp.bridge.BridgeScene;
public class ConsoleSceneViewIm implements ConsoleSceneView {
    
    private static ConsoleSceneViewIm instance;

    private ConsoleSceneViewIm() {
        BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        scene.getArthur().getArms().addPropertyChangeListener(this);
        scene.getGalahad().getArms().addPropertyChangeListener(this);
        scene.getLancelot().getArms().addPropertyChangeListener(this);
        scene.getRobin().getArms().addPropertyChangeListener(this);
        scene.getGuard().getArms().addPropertyChangeListener(this);
        List.of(
          scene.getArthur(),
          scene.getGalahad(),
          scene.getLancelot(),
          scene.getRobin(),
          scene.getGuard()
        );}
    public static ConsoleSceneViewIm getInstance() {
        if (instance == null) {
            instance = new ConsoleSceneViewIm();
        }
        return instance;
    }
    @Override
    public void propertyChange(PropertyChangeEvent evt){
        System.out.println(evt);
    }
}

//END OF FILE

//SESSION END

//SESSION START
35,Wed Jul 09 01:23:18 EDT 2025,147
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=362	+CONSOLE_SCENE_VIEW)%0Apublic static ConsoleSceneView consoleSceneViewFactoryMethod()%7B%0A  return ConsoleSceneViewIm.getInstance();%0A%7D%0A@Tags(Comp301Tags.	=79
//END OF FILE

//SESSION END

//SESSION START
36,Wed Jul 09 01:25:26 EDT 2025,0
//START OF FILE: main/Assignment2.java
package main;

import bus.uigen.*;
import mp.shapes.*;
import mp.bridge.*;

public class Assignment2 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotatingLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }

    public static void main(String[] args) throws InterruptedException {
    	BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

//END OF FILE
//START OF FILE: main/Assignment1.java
//@#$DELETED FILE&^%$
//END OF FILE

//SESSION END

//SESSION START
37,Wed Jul 09 01:30:43 EDT 2025,-30
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;

@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=65	-58	=3284
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public VShape getArms(){
    	return arms; 
    }
    @Override
    public VShape getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=166	+import main.StaticFactoryClass;	=1	-0	=215	-5	+Angl	=26	-5	+Angl	=68	-2	=69	-2	=2	-1	+StaticFactoryClass.l	=1	-5	+gsF	=1	-1	+ctoryM	=1	+thod	=3	-6	=19	-2	=2	-1	+StaticFactoryClass.l	=1	-5	+gsF	=1	-1	+ctoryM	=1	+thod	=670
//END OF FILE
//START OF FILE: main/ConsoleSceneViewIm.java
package main;

import java.beans.PropertyChangeEvent;
import java.util.List;
import mp.bridge.BridgeScene;
public class ConsoleSceneViewIm implements ConsoleSceneView {
    
    private static ConsoleSceneViewIm inst;

    private ConsoleSceneViewIm() {
        BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        scene.getArthur().getArms().addPropertyChangeListener(this);
        scene.getGalahad().getArms().addPropertyChangeListener(this);
        scene.getLancelot().getArms().addPropertyChangeListener(this);
        scene.getRobin().getArms().addPropertyChangeListener(this);
        scene.getGuard().getArms().addPropertyChangeListener(this);
        List.of(
          scene.getArthur(),
          scene.getGalahad(),
          scene.getLancelot(),
          scene.getRobin(),
          scene.getGuard()
        );}
    public static ConsoleSceneViewIm getInstance() {
        if (inst == null) {
            inst = new ConsoleSceneViewIm();
        }
        return inst;
    }
    @Override
    public void propertyChange(PropertyChangeEvent evt){
        System.out.println(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=71	-25	=145	-4	=702	-4	=28	-4	=58	-4	=120
//END OF FILE

//SESSION END

//SESSION START
38,Wed Jul 09 01:31:38 EDT 2025,-4
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=338	-5	+Angl	=17	-5	+Angl	=46
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=942	-5	+Angl	=63	-5	+Angl	=337
//END OF FILE

//SESSION END

//SESSION START
39,Wed Jul 09 01:33:13 EDT 2025,80
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import mp.shapes.RotatingLine;   
import util.models.PropertyListenerRegisterer;
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends PropertyListenerRegisterer{
    RotatingLine getLeftLine();
    RotatingLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=201	+import util.models.PropertyListenerRegisterer;	=103	+extends PropertyListenerRegisterer	=100
//END OF FILE

//SESSION END

//SESSION START
40,Wed Jul 09 01:34:46 EDT 2025,5
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.FACTORY_CLASS)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.FACTORY_CLASS)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.FACTORY_CLASS)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=185	+%0A	=18	-1	+FACTO	=1	-4	+Y	=1	+CLAS	=1	-4	=147	+FA	=1	-3	+T	=1	+RY_C	=1	-2	+AS	=1	-9	=129	+F	=1	-2	+CTORY_C	=1	-1	+ASS	=74
//END OF FILE

//SESSION END

//SESSION START
43,Wed Jul 09 01:39:55 EDT 2025,-420
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;


import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=110	-84	=635
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-84	=629
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;


import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=110	-84	=632
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-84	=628
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-84	=626
//END OF FILE

//SESSION END

//SESSION START
44,Wed Jul 09 01:41:22 EDT 2025,-8
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.FACTORY_CLASS)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.FACTORY_CLASS)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=506	-1	=1	-7	+NG	=1	-3	+E	=74
//END OF FILE

//SESSION END

//SESSION START
45,Wed Jul 09 01:43:21 EDT 2025,-4
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BOUNDED_SHAPE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.LOCATABLE)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=204	-4	+B	=1	-2	+UNDED	=1	-3	=1	-1	+HAPE	=147	-4	+L	=1	-3	=1	-1	=1	-2	+TABLE	=208
//END OF FILE

//SESSION END

//SESSION START
47,Wed Jul 09 01:45:48 EDT 2025,8
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod()
{
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=205	-3	+RI	=1	+G	=1	-1	=2	-3	+CEN	=148	+CONSO	=1	-1	+E_S	=1	-5	+EN	=1	+_VIEW	=208
//END OF FILE

//SESSION END

//SESSION START
49,Wed Jul 09 01:52:06 EDT 2025,-4
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape {
    String getText();
    void setText(String t);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=169	-26	=214
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import util.models.PropertyListenerRegisterer;
import util.annotations.Tags;
import util.annotations.Visible;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements PropertyListenerRegisterer {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    private void notify(String name, Object old, Object newv) {
        PropertyChangeEvent evt = new PropertyChangeEvent(this, name, old, newv);
        for (PropertyChangeListener l : propertyChangeListeners) {
            l.propertyChange(evt);
        }
    }
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }
    @Visible(false)
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return propertyChangeListeners;
    }
    public int getX() {
        return a + point.getX();
    }
    public void setX(int x) {
    	notify("X",this.a,x);
        this.a = x;
    }

    public int getY() {
        return b + point.getY();
    }
    public void setY(int y) {
    	notify("Y",this.b,y);
        this.b = y;
    }

    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(double r) {
    	notify("Radius", this.point.getRadius(), r);
        point = new APolarPoint(r, point.getAngle());
        notify("Width", null, getWidth());
        notify("Height", null, getHeight());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(double angle) {
    	notify("Angle", this.getAngle(), angle);
        point = new APolarPoint(point.getRadius(), angle);
        notify("Width", null, getWidth());
        notify("Height", null, getHeight());
    }

    public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
    }
   
}

(DIFF_FROM_PREVIOUS_FILE)
=843	+private void notify(String name, Object old, Object newv) %7B%0A        PropertyChangeEvent evt = new PropertyChangeEvent(this, name, old, newv);%0A        for (PropertyChangeListener l : propertyChangeListeners) %7B%0A            l.propertyChange(evt);%0A        %7D%0A    %7D%0A    	=10	-0	=365	-1	+not	=1	-2	+fy	=141	-1	+not	=1	-2	+fy	=291	-1	+not	=1	-2	+fy	=101	-1	+not	=1	-2	+fy	=37	-1	+not	=1	-2	+fy	=154	-1	+not	=1	-2	+fy	=102	-1	+not	=1	-2	+fy	=37	-1	+not	=1	-2	+fy	=221	+	=3	-258	=3
//END OF FILE

//SESSION END

//SESSION START
50,Wed Jul 09 01:53:48 EDT 2025,61
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags({
	  Comp301Tags.FACTORY_CLASS,
	  "legsFactoryMethod:*->@Comp301Tags.ANGLE"
	})
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=498	+%7B%0A%09  Comp301Tags.FACTORY_CLASS,%0A%09  %22legsFactoryMethod:*-%3E@	=17	+%22%0A%09%7D	=41	-1	=32
//END OF FILE

//SESSION END

//SESSION START
51,Wed Jul 09 01:54:08 EDT 2025,-62
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=498	-58	=17	-4	=73
//END OF FILE

//SESSION END

//SESSION START
53,Wed Jul 09 01:58:31 EDT 2025,163
//START OF FILE: mp/shapes/LocatableBean.java
package mp.shapes;
import util.models.PropertyListenerRegisterer;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.LOCATABLE)
public interface LocatableBean extends PropertyListenerRegisterer{

}

//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import util.annotations.Tags;
import util.annotations.Visible;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements LocatableBean {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    private void notify(String name, Object old, Object newv) {
        PropertyChangeEvent evt = new PropertyChangeEvent(this, name, old, newv);
        for (PropertyChangeListener l : propertyChangeListeners) {
            l.propertyChange(evt);
        }
    }
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }
    @Visible(false)
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return propertyChangeListeners;
    }
    public int getX() {
        return a + point.getX();
    }
    public void setX(int x) {
    	notify("X",this.a,x);
        this.a = x;
    }

    public int getY() {
        return b + point.getY();
    }
    public void setY(int y) {
    	notify("Y",this.b,y);
        this.b = y;
    }

    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(double r) {
    	notify("Radius", this.point.getRadius(), r);
        point = new APolarPoint(r, point.getAngle());
        notify("Width", null, getWidth());
        notify("Height", null, getHeight());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(double angle) {
    	notify("Angle", this.getAngle(), angle);
        point = new APolarPoint(point.getRadius(), angle);
        notify("Width", null, getWidth());
        notify("Height", null, getHeight());
    }

    public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
    }
   
}

(DIFF_FROM_PREVIOUS_FILE)
=124	-47	=329	-2	+L	=1	-3	+ca	=1	-5	+ableB	=1	+a	=1	-12	=2094
//END OF FILE

//SESSION END

//SESSION START
54,Wed Jul 09 02:06:24 EDT 2025,922
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    private final List<PropertyChangeListener> listeners = new ArrayList<>();
    public ArthurHead() {
    }
    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;	=176	+rivate final List%3CPropertyChangeListener%3E listeners = new ArrayList%3C%3E();%0A    p	=20	+%0A    %7D%0A    @Override%0A    public void addPropertyChangeListener(PropertyChangeListener l) %7B%0A        listeners.add(l);	=275	+%0A	=1	+   %09PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);	=23	+%09for (var l : listeners) l.propertyChange(evt);%0A    	=39	-1	=73	+PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);%0A    %09	=12	+%0A    %09for (var l : listeners) l.propertyChange(evt);	=9
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.models.PropertyListenerRegisterer;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends PropertyListenerRegisterer{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import util.models.PropertyListenerRegisterer;%0A	=84	+extends PropertyListenerRegisterer	=149
//END OF FILE
//START OF FILE: main/ConsoleSceneViewIm.java
package main;

import java.beans.PropertyChangeEvent;
import java.util.List;

import mp.bridge.Avatar;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public class ConsoleSceneViewIm implements ConsoleSceneView {
    
    private static ConsoleSceneViewIm inst;

    private ConsoleSceneViewIm() {
        BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        for (Avatar knight : List.of(
                scene.getArthur(),
                scene.getGalahad(),
                scene.getLancelot(),
                scene.getRobin(),
                scene.getGuard())) {
            knight.getHead().addPropertyChangeListener(this);
            knight.getStringShape().addPropertyChangeListener(this);
            knight.getArms().getLeftLine().addPropertyChangeListener(this);
            knight.getArms().getRightLine().addPropertyChangeListener(this);
            knight.getLegs().getLeftLine().addPropertyChangeListener(this);
            knight.getLegs().getRightLine().addPropertyChangeListener(this);}
    public static ConsoleSceneViewIm getInstance() {
        if (inst == null) {
            inst = new ConsoleSceneViewIm();
        }
        return inst;
    }
    @Override
    public void propertyChange(PropertyChangeEvent evt){
        System.out.println(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=77	+%0Aimport mp.bridge.Avatar;%0A	=30	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A%0A@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)%0A	=222	+        for (Avatar knight : List.of(%0A        	=25	+,%0A                scene	=4	-4	+Galahad	=2	+,%0A                scene	=1	-7	+g	=1	-1	=1	-3	+L	=2	-1	+c	=1	-2	+lot(),%0A                	=1	-1	+c	=3	-2	+.ge	=1	-1	+Rob	=1	-1	+n(	=1	-1	+,	=1	+        	=18	+u	=1	-4	+r	=3	+)) %7B%0A            knight	=4	-4	+Head	=44	-3	+    k	=1	-2	+i	=1	-9	+h	=1	-2	=4	-1	+St	=1	-2	+ingShape	=44	-3	+    k	=1	-1	+ight	=4	-5	+Arms	=6	-4	+LeftLine	=44	-3	+    k	=1	-1	+ight	=4	-3	+A	=1	-1	+ms	=6	-4	+RightLine	=44	-12	=4	-6	+k	=1	-1	+ight	=4	-6	+Legs	=2	-14	+.getL	=1	+ftLi	=2	+()	=1	-1	+addProp	=1	+r	=1	-4	+yC	=2	-1	+ngeListener	=1	+this	=1	-1	+;	=11	-3	+  k	=1	-1	+ight	=5	-3	=1	-3	+gs	=2	-17	=5	-2	+ightL	=2	+e	=2	-14	+.addProp	=1	+rtyCha	=1	-2	=2	+Lis	=1	-3	+ene	=1	-1	=1	-10	+this	=279
//END OF FILE

//SESSION END

//SESSION START
55,Wed Jul 09 02:09:50 EDT 2025,2283
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    private final List<PropertyChangeListener> listeners = new ArrayList<>();

    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=20	-1	=89	+import java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;	=209	+    private final List%3CPropertyChangeListener%3E listeners = new ArrayList%3C%3E();%0A%0A    @Override%0A    public void addPropertyChangeListener(PropertyChangeListener l) %7B%0A        listeners.add(l);%0A    %7D%0A	=229	-14	=25	+%0A	=1	+   %09PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);	=23	+%09for (var l : listeners) l.propertyChange(evt);%0A    	=39	-1	=73	+PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);%0A    %09	=12	+%0A    %09for (var l : listeners) l.propertyChange(evt);	=9
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    private final List<PropertyChangeListener> listeners = new ArrayList<>();

    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=20	-1	=89	+import java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;	=173	+    %0A	=114	+private final List%3CPropertyChangeListener%3E listeners = new ArrayList%3C%3E();%0A%0A    @Override%0A    public void addPropertyChangeListener(PropertyChangeListener l) %7B%0A        listeners.add(l);%0A    %7D%0A    	=148	-14	=25	+%0A	=1	+   %09PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);	=23	+%09for (var l : listeners) l.propertyChange(evt);%0A    	=39	-1	=73	+PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);%0A    %09	=12	+%0A    %09for (var l : listeners) l.propertyChange(evt);	=9
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }
    private final List<PropertyChangeListener> listeners = new ArrayList<>();

    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;	=202	+    private final List%3CPropertyChangeListener%3E listeners = new ArrayList%3C%3E();%0A%0A    @Override%0A    public void addPropertyChangeListener(PropertyChangeListener l) %7B%0A        listeners.add(l);%0A    %7D%0A	=229	-14	=25	+%0A	=1	+   %09PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);	=23	+%09for (var l : listeners) l.propertyChange(evt);%0A    	=39	-1	=73	+PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);%0A    %09	=12	+%0A    %09for (var l : listeners) l.propertyChange(evt);	=9
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    private final List<PropertyChangeListener> listeners = new ArrayList<>();

    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import java.beans.PropertyChangeEvent;%0Aimport java.beans.PropertyChangeListener;%0Aimport java.util.ArrayList;%0Aimport java.util.List;	=200	+    private final List%3CPropertyChangeListener%3E listeners = new ArrayList%3C%3E();%0A%0A    @Override%0A    public void addPropertyChangeListener(PropertyChangeListener l) %7B%0A        listeners.add(l);%0A    %7D%0A	=229	-14	=25	+%0A	=1	+   %09PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);	=23	+%09for (var l : listeners) l.propertyChange(evt);%0A    	=39	-1	=73	+PropertyChangeEvent evt = new PropertyChangeEvent(this, %22X%22, this.x, x);%0A    %09	=12	+%0A    %09for (var l : listeners) l.propertyChange(evt);	=9
//END OF FILE

//SESSION END

//SESSION START
56,Wed Jul 09 02:11:05 EDT 2025,42
//START OF FILE: main/ConsoleSceneViewIm.java
package main;

import java.beans.PropertyChangeEvent;
import java.util.List;

import mp.bridge.Avatar;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public class ConsoleSceneViewIm implements ConsoleSceneView {
    
    private static ConsoleSceneViewIm inst;

    private ConsoleSceneViewIm() {
        BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        scene.getArthur().getArms().addPropertyChangeListener(this);
        scene.getArthur().getLegs().addPropertyChangeListener(this);

        scene.getGalahad().getArms().addPropertyChangeListener(this);
        scene.getGalahad().getLegs().addPropertyChangeListener(this);

        scene.getLancelot().getArms().addPropertyChangeListener(this);
        scene.getLancelot().getLegs().addPropertyChangeListener(this);

        scene.getRobin().getArms().addPropertyChangeListener(this);
        scene.getRobin().getLegs().addPropertyChangeListener(this);

        scene.getGuard().getArms().addPropertyChangeListener(this);
        scene.getGuard().getLegs().addPropertyChangeListener(this);}
    public static ConsoleSceneViewIm getInstance() {
        if (inst == null) {
            inst = new ConsoleSceneViewIm();
        }
        return inst;
    }
    @Override
    public void propertyChange(PropertyChangeEvent evt){
        System.out.println(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=460	-2	+scene.getArthu	=1	-1	=1	+).get	=1	-1	+rms().	=1	+ddProper	=1	+yCh	=1	-3	=1	-1	=1	-5	+e	=4	-3	+ener	=1	+this);	=1	-8	=25	-1	+.getLegs().addPropertyChangeListener(this);%0A	=9	-8	=18	-18	+.getArm	=1	-1	+().addProp	=1	+rtyCha	=1	-2	=2	+Lis	=1	-2	+e	=1	-1	=1	-2	+r(	=1	-1	+his	=1	-1	+;	=1	-4	=8	-4	=9	-1	+Galahad().getLegs().addPr	=1	-1	+pertyChangeL	=1	+ste	=1	+er	=1	+this	=1	-1	+;%0A	=1	-2	=8	-6	=9	-2	+L	=1	-22	=1	-3	+celo	=1	+()	=4	-4	+Arms	=44	-5	+sce	=1	-4	+e	=4	-4	+La	=1	+celot().	=1	-4	=1	+tLegs	=36	-4	+%0A	=8	-1	+sce	=1	-1	+e.	=1	-1	+e	=1	+Robin()	=8	-14	=44	-5	+sce	=1	-4	+e	=4	-4	+Robin	=6	-5	=1	-2	=1	+gs	=36	-4	+%0A	=8	-1	+sce	=1	-4	+e	=4	-4	+Guard	=6	-8	+Arms	=44	-5	+sce	=1	-4	+e	=4	-4	+Guard	=6	-5	=1	-2	=1	+gs	=312
//END OF FILE

//SESSION END

//SESSION START
58,Wed Jul 09 02:13:57 EDT 2025,70
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
public static Angle armsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=587	+public static Angle armsFactoryMethod()%7B%0A%09   return new VShape();%09%09%0A%7D%0A	=1
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.armsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=665	-3	+arm	=682
//END OF FILE

//SESSION END

//SESSION START
59,Wed Jul 09 02:15:21 EDT 2025,25
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneView consoleSceneViewFactoryMethod(){
  return ConsoleSceneViewIm.getInstance();
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
@Tags(Comp301Tags.ANGLE)
public static Angle armsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=587	+@Tags(Comp301Tags.ANGLE)%0A	=71
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.armsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=605	-3	+arm	=57	-3	+leg	=682
//END OF FILE

//SESSION END

//SESSION START
61,Wed Jul 09 02:17:22 EDT 2025,89
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle{
	int x, y, width, height;
	public AScalableRectangle(int theX, int theY, int theWidth, int theHeight) {
		x = theX;
		y = theY;
		width = theWidth;
		height = theHeight;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/100;
		height = (height*percentage)/100;		
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=19	+import tags301.Comp301Tags;	=43	+import util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)	=609
//END OF FILE

//SESSION END

//SESSION START
62,Wed Jul 09 02:19:07 EDT 2025,267
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;

public interface AScalableRectangleInterface {
	public int getX();
	public int getY();
	public int getWidth();	
	public int getHeight();	
	public void setHeight(int newVal);
	public void setWidth(int newVal);
	public void scale(int percentage);
}

//END OF FILE

//SESSION END

//SESSION START
63,Wed Jul 09 02:19:43 EDT 2025,39
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface{
	int x, y, width, height;
	public AScalableRectangle(int theX, int theY, int theWidth, int theHeight) {
		x = theX;
		y = theY;
		width = theWidth;
		height = theHeight;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/100;
		height = (height*percentage)/100;		
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=222	+ implements AScalableRectangleInterface	=538
//END OF FILE

//SESSION END

//SESSION START
64,Wed Jul 09 02:20:10 EDT 2025,171
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public interface AScalableRectangleInterface {
	public int getX();
	public int getY();
	public int getWidth();	
	public int getHeight();	
	public void setHeight(int newVal);
	public void setWidth(int newVal);
	public void scale(int percentage);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)%0A@StructurePattern(%22Rectangle Pattern%22)	=248
//END OF FILE

//SESSION END

//SESSION START
65,Wed Jul 09 02:21:51 EDT 2025,90
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.models.PropertyListenerRegisterer;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends PropertyListenerRegisterer{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=121	+annotations.Tags;%0Aimport tags301.Comp301Tags;%0Aimport util.	=35	+@Tags(Comp301Tags.BOUNDED_SHAPE)	=267
//END OF FILE

//SESSION END

//SESSION START
66,Wed Jul 09 02:22:39 EDT 2025,90
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    private final List<PropertyChangeListener> listeners = new ArrayList<>();
    public ArthurHead() {
    }
    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=241	+import util.annotations.Tags;%0Aimport tags301.Comp301Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)%0A	=1080
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import util.models.PropertyListenerRegisterer;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends PropertyListenerRegisterer{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import util.models.PropertyListenerRegisterer;	=146	-47	=301
//END OF FILE

//SESSION END

//SESSION START
67,Wed Jul 09 02:23:00 EDT 2025,-90
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    private final List<PropertyChangeListener> listeners = new ArrayList<>();
    public ArthurHead() {
    }
    @Override
    public void addPropertyChangeListener(PropertyChangeListener l) {
        listeners.add(l);
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.x = x; 
    	for (var l : listeners) l.propertyChange(evt);
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	PropertyChangeEvent evt = new PropertyChangeEvent(this, "X", this.x, x);
    	this.y = y; 
    	for (var l : listeners) l.propertyChange(evt);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=241	-90	=1081
//END OF FILE

//SESSION END

//SESSION START
68,Wed Jul 09 03:21:42 EDT 2025,1434
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public LancelotHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) {
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=141	-2	+List	=2	-1	+er	=34	-22	+S	=1	-17	+p	=4	-15	=1	+%0A	=173	+, width, height	=7	-2	+rivate fina	=1	-2	=1	-1	+PropertyCh	=2	-1	+g	=1	-1	+Supp	=1	+r	=1	-1	+ pcs = n	=1	+w PropertyCh	=1	-1	+ngeSupport	=1	+this	=1	-2	+;	=5	-1	+@Override	=6	-1	+ubl	=1	+c 	=1	+oid 	=1	-1	+ddProp	=1	-4	+rtyCh	=1	-2	+nge	=4	-1	+ener(	=22	-1	=9	-1	+) %7B%0A	=1	-1	=1	-3	=1	-1	+     pcs.addP	=1	+ope	=1	+tyCh	=1	-1	+nge	=4	-2	+ener	=1	+listener	=2	-1	=5	-9	+%7D%0A	=17	-3	+remove	=47	+istener	=12	+pcs.removePropertyChangeListener(	=8	-2	+);%0A    %7D%0A    %0A    public LancelotHe	=1	-1	=2	-1	=1	-1	+ %7B	=11	+%0A    	=21	-3	=2	-1	+t	=4	-6	+W	=1	-6	+dth	=2	-1	=1	-7	=7	-2	+width	=1	-1	+%7D	=5	-1	=34	-6	+Width(int x) %7B%0A    %09pcs.f	=1	-1	+r	=1	-1	+PropertyCh	=1	-1	+ng	=2	-1	+%22Width%22, 	=1	-1	+h	=1	-2	+s.width,	=1	-2	+x	=1	-3	+;	=11	-2	+width	=3	-2	+x	=1	-1	=39	-1	+Height	=4	-7	=7	-1	+height	=1	-1	+%7D	=5	-1	+@Override	=20	-1	+Height	=15	-3	=1	-1	+cs.fi	=1	-7	=1	-16	=14	-2	+(%22H	=1	-5	=1	-5	+ght	=8	-1	+height	=16	-1	+height	=5	+%0A   	=1	+%7D	=5	-3	+@Over	=1	+ide%0A	=1	-4	=1	-1	=1	-1	=1	+pub	=2	-1	+c S	=1	-1	+ri	=1	-4	+g	=1	-6	+g	=1	-1	=1	-3	+Im	=1	-1	=2	+FileName	=1	+) %7B %0A    %09r	=1	-1	=1	-1	+urn fn	=1	+ 	=32	+vo	=1	-1	+d se	=1	-1	+Ima	=2	-2	+FileName	=1	+String fn	=3	+ 	=6	-2	=1	-2	+his.f	=2	-1	+= fn	=34	-2	=1	-1	+nt	=1	-1	+g	=2	-1	+X	=1	-5	=10	-1	=1	-2	=1	-1	=1	-4	+ur	=1	-7	+ x; %0A    %7D%0A	=1	-3	=1	-1	=1	-3	=1	-3	=1	-11	+ublic 	=1	+oid s	=1	-1	=1	+X	=1	-2	=1	-8	+n	=1	-6	=3	-1	+ %7B	=11	-1	+x	=3	-1	+x	=3	+ 	=2	+ %7D%0A	=2	-4	+ 	=1	-1	+@O	=1	-1	+e	=1	+ride%0A	=1	-1	+ 	=1	-1	=1	+pub	=2	-1	+c in	=1	+ g	=1	+tY() %7B%0A    %09retur	=1	+ y; %0A    %7D%0A    @Ov	=2	-2	+ride%0A   	=1	-2	=1	-1	+ublic v	=1	-1	+id s	=1	-1	=1	-7	+Y	=1	-2	+in	=1	+ y	=1	+ %7B%0A    %09this.y = y	=1	+ 	=9
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    public ArthurHead() {
    }
    @Override
    public int getWidth(){return width;}
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    
}

(DIFF_FROM_PREVIOUS_FILE)
=202	-8	+be	=1	-3	+n	=1	-6	+.Pr	=1	+pe	=2	-4	+yCh	=1	-1	+ngeS	=1	-7	+ppor	=172	+, width, height	=20	+PropertyChangeSupport pcs = new PropertyChangeSupport(this);%0A    @Override%0A    public void addPropertyChange	=4	-1	+ener(	=22	-1	=9	-1	+) %7B%0A      	=1	-1	=1	+pcs.addPropertyChangeListe	=2	-1	+r(listener);%0A    %7D%0A%0A   	=1	-1	+public void 	=1	+emovePrope	=1	+tyCh	=1	+ngeListener(Propert	=1	+Change	=4	-2	+ener listener) %7B%0A        pcs.removePropertyChangeListener	=1	+listener	=7	+%7D%0A    	=53	-2	=1	-1	+nt	=1	-2	+getWi	=1	-1	+th()%7B	=1	-2	=1	+tu	=1	+n wid	=1	-2	=1	-3	+;%7D%0A    @Ov	=1	-1	+rride%0A    publ	=1	+c void 	=1	+etWid	=1	-1	+h(i	=1	-1	+t x) %7B%0A    %09pcs.fi	=1	-1	+e	=14	-1	+(%22Width%22, th	=2	+.wid	=1	-2	+h, x);%0A    %09this.width = x;%0A    %7D%0A    @Ov	=2	+ride%0A  	=1	+ pub	=1	+ic int getHeight(	=3	+return height;%7D	=5	+@Override%0A	=4	+pub	=2	+c void 	=1	+e	=1	+H	=1	+ight(i	=1	-2	+t x) %7B%0A    %09pc	=2	+firePropertyCh	=1	-2	+nge	=1	-1	+%22Height%22, this.height, x	=1	+;%0A    %09this.height = x	=282	-78	=17	-52	=112	-78	=17	-47	+%7D	=5	-1	=3
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public GalahadHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }

    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=141	-2	+List	=2	-1	+er	=34	-22	+S	=1	-17	+p	=4	-15	=1	+%0A	=171	-2	+,	=1	+width,	=1	-2	+height;	=6	-3	+r	=1	-1	+vate	=1	-1	+fin	=2	-1	+ PropertyC	=2	-2	+ng	=1	-7	+Support	=1	+pcs	=1	+=	=1	+new	=1	-1	+PropertyChangeSupport(this);	=26	-3	+vo	=1	-2	+d	=1	-1	+addProp	=1	+r	=1	-2	+yCh	=1	+n	=2	-1	+L	=1	-1	+st	=1	-3	+n	=1	+r	=1	-10	+Prope	=1	+tyChang	=1	+Lis	=1	-2	+e	=1	+er	=1	-1	+liste	=1	-1	+er)	=1	+%7B	=5	-2	=5	-3	+cs.	=1	-14	+dd	=22	-2	+(	=8	-20	=2	-1	=5	-9	+%7D%0A	=17	-3	+remove	=47	+istener	=12	-1	+pcs.removePropertyChangeL	=7	-5	=2	+istener	=13	-9	=12	-10	+G	=1	-4	=1	+ahadH	=1	-1	=1	-2	+d	=1	-9	=3	-1	=5	-15	+%7D	=5	-1	=33	-1	+Width	=2	-1	=1	-7	=7	-1	+width	=1	+%7D%0A   	=6	-1	+@Override	=20	-1	+Width	=15	-3	=1	-1	+cs.fi	=1	-21	=1	-2	=14	-5	=1	+%22Wid	=2	-6	=8	-1	+width	=16	-1	+width	=5	+%0A  	=1	+ %7D	=5	-3	+@Over	=1	+ide%0A	=1	+   public int getHeight	=1	+) %7Breturn height;%7D%0A    @O	=1	-1	+er	=1	+ide%0A	=1	-1	=1	-1	=1	+ pub	=2	+c void 	=1	+e	=1	+H	=1	+ight(i	=1	-3	+t x	=2	-2	+%7B%0A    %09	=1	+cs.fireP	=14	+%22H	=1	-1	+igh	=1	+%22, this.height, x	=7	+%09this.height = x;%0A    	=27	+Str	=2	-1	+g	=4	-1	+ImageFileName	=4	+ 	=13	-1	+fn	=9	+%0A	=33	-1	+ImageFileName	=1	+Str	=2	-1	+g	=1	-1	+fn	=10	-6	=1	-2	=1	-1	+is.f	=1	-3	+ = fn; %0A    %7D%0A    @O	=2	+rride%0A    public i	=3	+g	=1	-1	=1	+X() %7B %0A	=1	-1	=1	-3	+ 	=1	-1	+%09	=1	-2	=1	-1	=1	-4	+ur	=1	-3	+ x; %0A    %7D%0A    public 	=1	+oid s	=1	-1	=1	+X	=1	-2	=1	-8	+n	=1	-6	=3	-1	+ %7B	=11	-1	+x	=3	-1	+x	=7	-4	+%7D%0A	=1	-1	+   @O	=1	-1	+e	=1	+ride%0A	=1	-1	+ 	=1	-1	=1	+pub	=2	-1	+c in	=1	+ g	=1	-4	+tY(	=2	-3	+%7B%0A    %09	=1	-2	=1	-1	=1	-4	+ur	=1	-1	+ y; %0A    %7D%0A    @Ov	=1	-1	+rrid	=1	+%0A    public 	=1	+oid se	=1	+Y(int y	=1	+ %7B %0A    %09this.y = y	=1	+ 	=9
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public GuardHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=141	-2	+List	=2	-1	+er	=34	-22	+S	=1	-17	+p	=4	-15	=1	+%0A	=167	+, width, height	=7	-3	+r	=1	-1	+vate	=1	-2	+fin	=1	+l P	=1	-2	+op	=1	+rtyCh	=1	-3	+ngeSupport	=1	-1	+pcs	=1	-1	+= new PropertyChangeSupport(this);	=5	-1	+@Override	=6	-1	+ubl	=1	+c 	=1	+oid 	=1	-1	+ddProp	=1	-4	+rtyCh	=1	-2	+nge	=4	-1	+ener(	=22	-1	=9	-1	+)	=1	-1	+%7B%0A      	=1	-3	=1	-1	+pcs.addP	=1	+ope	=1	+tyCh	=1	-1	+nge	=4	-2	+ener	=1	+listener	=2	-1	=5	-9	+%7D%0A	=17	-3	+remove	=47	+istener	=12	+pcs.removePropertyChangeListener(	=8	-2	+);%0A    %7D%0A    %0A    public Gu	=1	+r	=1	+Hea	=2	-1	=1	-1	+ %7B	=11	+%0A    	=21	-3	=2	-1	+t	=4	-6	+W	=1	-6	+dth	=2	-1	=1	-7	=7	-2	+width	=1	-1	+%7D	=5	-1	=34	-6	+Width(int x) %7B%0A    %09pcs.f	=1	-1	+r	=1	-1	+PropertyCh	=1	-1	+ng	=2	-1	+%22Width%22, 	=1	-1	+h	=1	-2	+s.width,	=1	-2	+x	=1	-3	+;	=11	-2	+width	=3	-2	+x	=1	-1	=39	-1	+Height	=4	-7	=7	-1	+height	=1	-1	+%7D	=5	-1	+@Override	=20	-1	+Height	=15	-3	=1	-1	+cs.fi	=1	-7	=1	-16	=14	-5	=1	-6	=1	-1	+Height	=8	-1	+height	=16	-1	+height	=5	+%0A 	=1	+  %7D	=5	-3	+@Over	=1	+ide%0A	=1	-4	=1	-1	=1	-1	=1	+pub	=2	-1	+c S	=1	-3	=1	-2	+ing	=1	-6	+g	=1	-1	=1	-3	+Im	=1	-1	=2	+FileName	=1	+) %7B %0A    %09r	=1	-1	=1	-1	+urn fn	=1	+ 	=32	+vo	=1	-2	+d	=1	+setIma	=2	+FileName(S	=1	-2	+ring fn	=3	+ 	=6	-2	=1	-2	+his.f	=2	-1	+= fn	=34	-2	=1	-1	+nt	=1	-1	+g	=2	-1	+X	=1	-5	=10	-1	=1	-2	=1	-1	=1	-4	+ur	=1	-7	+ x; %0A    %7D%0A	=1	-3	=1	-1	=1	-3	=1	-3	=1	-11	+ublic 	=1	+oid s	=1	-1	=1	+X	=1	-2	=1	-8	+n	=1	-6	=3	-1	+ %7B	=11	-1	+x	=3	-1	+x	=7	-4	+%7D%0A   	=1	-1	+@O	=1	-1	+er	=1	+ide%0A	=1	-1	=1	-1	+ 	=1	+pub	=2	-1	+c in	=1	+ getY() %7B%0A    %09r	=1	+tur	=1	+ y; %0A    %7D%0A    @Ov	=2	-2	+ride%0A   	=1	-2	=1	-1	+ublic v	=1	-1	+id s	=1	-1	=1	-7	+Y	=1	-2	+in	=1	+ y	=1	+ %7B %0A    %09this.y = y	=1	+ 	=9
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import util.models.PropertyListenerRegisterer;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends PropertyListenerRegisterer{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
    int getWidth();
    void setWidth(int x);
    int getHeight();
    void setHeight(int x);
}

(DIFF_FROM_PREVIOUS_FILE)
=510	+    int getWidth();%0A    void setWidth(int x);%0A    int getHeight();%0A    void setHeight(int x);%0A	=2
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public RobinHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) {
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=141	-2	+List	=2	-1	+er	=34	-22	+S	=1	-17	+p	=3	-15	=169	+, width, height	=7	-3	+r	=1	-1	+vate	=1	-3	+f	=2	-1	+al Prop	=1	+rtyCh	=1	-1	+ngeSupport pcs = new PropertyChangeSupport	=1	+this	=1	-2	+;	=5	-1	+@Override	=6	-1	+ubl	=1	+c 	=1	+oid 	=1	-1	+ddProp	=1	-4	+rtyCh	=1	-2	+nge	=4	-1	+ener(	=22	-1	=9	-1	+)	=1	-1	+%7B%0A	=1	-3	+      	=1	-1	+pcs.addP	=1	+ope	=1	+tyCh	=1	-1	+nge	=4	-2	+ener	=1	+listener	=2	-1	=5	-9	+%7D%0A	=17	-3	+remove	=47	+istener	=12	+pcs.removePropertyChangeListener(	=8	-2	+);%0A    %7D%0A    %0A    public RobinHe	=1	-1	=2	-1	=1	-1	+ %7B	=11	+%0A    	=21	-3	=2	-1	+t	=4	-6	+W	=1	-6	+dth	=2	-1	=1	-7	=7	-2	+width	=1	-1	+%7D	=5	-1	=34	-6	+Width(int x) %7B%0A    %09pcs.f	=1	-1	+r	=1	-1	+PropertyCh	=1	-1	+ng	=2	-1	+%22Width%22, 	=1	-1	+h	=1	-2	+s.width,	=1	-2	+x	=1	-3	+;	=11	-2	+width	=3	-2	+x	=1	-1	=39	-1	+Height	=4	-7	=7	-1	+height	=1	-1	+%7D	=5	-1	+@Override	=20	-1	+Height	=15	-3	=1	-1	+cs.fi	=1	-7	=1	-16	=14	-2	+(%22H	=1	-5	=1	-5	+ght	=8	-1	+height	=16	-1	+height	=5	+%0A   	=1	+%7D	=5	-3	+@Over	=1	+ide%0A	=1	-4	=1	-1	=1	-1	=1	+pub	=2	-1	+c S	=1	-1	+ri	=1	-4	+g	=1	-6	+g	=1	-1	=1	-3	+Im	=1	-1	=2	+FileName	=1	+) %7B %0A    %09r	=1	-1	=1	-1	+urn fn	=1	+ 	=32	+vo	=1	-1	+d se	=1	-1	+Ima	=2	-2	+FileName	=1	+String fn	=3	+ 	=6	-2	=1	-2	+his.f	=2	-1	+= fn	=34	-2	=1	-1	+nt	=1	-1	+g	=2	-1	+X	=1	-5	=10	-1	=1	-2	=1	-1	=1	-4	+ur	=1	-7	+ x; %0A    %7D%0A	=1	-3	=1	-1	=1	-3	=1	-3	=1	-11	+ublic 	=1	+oid s	=1	-1	=1	+X	=1	-2	=1	-8	+n	=1	-6	=3	-1	+ %7B	=11	-1	+x	=3	-1	+x	=3	+ 	=2	+ %7D%0A	=2	-4	+ 	=1	-1	+@O	=1	-1	+e	=1	+ride%0A	=1	-1	+ 	=1	-1	=1	+pub	=2	-1	+c in	=1	+ g	=1	+tY() %7B%0A    %09retur	=1	+ y; %0A    %7D%0A    @Ov	=2	-2	+ride%0A   	=1	-2	=1	-1	+ublic v	=1	-1	+id s	=1	-1	=1	-7	+Y	=1	-2	+in	=1	+ y	=1	+ %7B%0A    %09this.y = y	=1	+ 	=9
//END OF FILE

//SESSION END

//SESSION START
69,Wed Jul 09 03:23:21 EDT 2025,414
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public LancelotHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) {
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=192	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)	=1385
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;


import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    public ArthurHead() {
    }
    @Override
    public int getWidth(){return width;}
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
    
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-38	+%0A	=84	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)	=1384
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public GalahadHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }

    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=192	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)	=1384
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public GuardHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=192	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)	=1377
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y, width, height;
    private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        pcs.addPropertyChangeListener(listener);
    }

    public void removePropertyChangeListener(PropertyChangeListener listener) {
        pcs.removePropertyChangeListener(listener);
    }
    
    public RobinHead() {
    }
    
    @Override
    public int getWidth(){return width;}
    
    @Override
    public void setWidth(int x) {
    	pcs.firePropertyChange("Width", this.width, x);
    	this.width = x;
    }
    @Override
    public int getHeight() {return height;}
    @Override
    public void setHeight(int x) {
    	pcs.firePropertyChange("Height", this.height, x);
    	this.height = x;
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) {
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=192	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)%0A	=1375
//END OF FILE

//SESSION END

//SESSION START
70,Wed Jul 09 03:31:37 EDT 2025,-4743
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
  
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	
    	this.y = y; 
    	
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-174	=172	-15	=7	-369	=22	-383	=280	+  %0A	=109	+ %0A    %09	=18	+%0A    %09	=9
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-1	+import java.beans.PropertyChangeEvent;	=55	-5	+util	=1	-1	+A	=1	-3	=1	-4	=1	-9	+yLis	=10	-1	+j	=1	-5	+va	=1	-20	=5	-7	+L	=1	-2	=1	-5	+t	=1	-33	=169	-15	=7	-364	=26	-387	=408	-5	=3
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;


import java.beans.PropertyChangeListener;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
	@Override
	public void addPropertyChangeListener(PropertyChangeListener arg0) {
	}
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+%0Aimport java.beans.PropertyChangeListener;%0A%0A	=89	-173	=171	-15	=7	-369	=21	-383	=84	-1	=152	+@Override%0A    	=25	+ 	=62	+ 	=90	+%7D%0A%09@Override%0A%09public void addPropertyChangeListener(PropertyChangeListener arg0) %7B%0A%09	=4
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-174	=166	-15	=7	-369	=19	-123	=2	-68	=7	-203	=216	+@Override%0A    	=25	+ 	=62	+ 	=94
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;

public interface AScalableRectangleInterface {
	public int getX();
	public int getY();
	public int getWidth();	
	public int getHeight();	
	public void setHeight(int newVal);
	public void setWidth(int newVal);
	public void scale(int percentage);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	-171	=248
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=31	-47	=30	+	=220	-35	=125	+	=19	-94	=5
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	-173	=167	-15	=7	-369	=19	-383	=236	+@Override%0A    	=25	+ 	=62	+ 	=66	+ 	=27
//END OF FILE

//SESSION END

//SESSION START
71,Wed Jul 09 03:32:33 EDT 2025,87
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;


import java.beans.PropertyChangeListener;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=783	-84	=2
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public interface AScalableRectangleInterface {
	public int getX();
	public int getY();
	public int getWidth();	
	public int getHeight();	
	public void setHeight(int newVal);
	public void setWidth(int newVal);
	public void scale(int percentage);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)%0A@StructurePattern(%22Rectangle Pattern%22)	=248
//END OF FILE

//SESSION END

//SESSION START
73,Wed Jul 09 03:34:41 EDT 2025,90
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=184	+import util.annotations.Tags;%0Aimport tags301.Comp301Tags;%0A%0A@Tags(Comp301Tags.BRIDGE_SCENE)	=3165
//END OF FILE

//SESSION END

//SESSION START
75,Wed Jul 09 03:38:20 EDT 2025,287
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int x) {
    	this.x = x; 
  
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	
    	this.y = y; 
    	
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import util.annotations.Tags;%0Aimport tags301.Comp301Tags;%0A@Tags(Comp301Tags.AVATAR)%0A	=633
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=116	-4	+util	=1	-2	=2	-4	+n	=1	-3	=1	-3	=1	-7	=1	-2	=1	-2	=1	-11	=3	-10	+T	=1	-1	=1	-3	=1	-5	=9	-3	+t	=1	+gs301	=1	-8	+Comp301T	=1	-3	+g	=1	-1	=2	-1	+@Tags(Co	=2	-5	+301T	=1	-2	+gs	=1	-10	+AVATAR)	=613
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;


import java.beans.PropertyChangeListener;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=153	+import util.annotations.Tags;%0Aimport tags301.Comp301Tags;%0A@Tags(Comp301Tags.AVATAR)	=632
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import util.annotations.Tags;%0Aimport tags301.Comp301Tags;%0A@Tags(Comp301Tags.AVATAR)%0A	=614
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.armsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=19	-57	=119	+s;%0Aimport util.annotations.Tags;%0Aimport tags301.Comp301Tag	=1155
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=109	+import util.annotations.Tags;%0Aimport tags301.Comp301Tags;%0A@Tags(Comp301Tags.AVATAR)	=626
//END OF FILE

//SESSION END

//SESSION START
76,Wed Jul 09 03:38:57 EDT 2025,27
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape {
    String getText();
    void setText(String t);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=169	+Tags(Comp301Tags.AVATAR)%0A@	=214
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;


import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape {
    private String text = "Grail";
    private int x, y;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return x;
    }
    @Override
    public void setX(int x) { 
    	this.x = x; }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+%0A	=687
//END OF FILE

//SESSION END

//SESSION START
78,Wed Jul 09 03:41:24 EDT 2025,-1
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=65	+tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0Aimport 	=118	-59	=3197
//END OF FILE

//SESSION END

//SESSION START
79,Wed Jul 09 03:42:07 EDT 2025,-1
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import mp.shapes.RotatingLine;   
import util.models.PropertyListenerRegisterer
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends PropertyListenerRegisterer{
    RotatingLine getLeftLine();
    RotatingLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=246	-1	=237
//END OF FILE

//SESSION END

//SESSION START
80,Wed Jul 09 03:43:50 EDT 2025,1
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import mp.shapes.RotatingLine;   
import util.models.PropertyListenerRegisterer;
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends PropertyListenerRegisterer{
    RotatingLine getLeftLine();
    RotatingLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=246	+;	=237
//END OF FILE

//SESSION END

//SESSION START
82,Wed Jul 09 03:48:07 EDT 2025,-1449
//START OF FILE: mp/shapes/LocatableBean.java
//@#$DELETED FILE&^%$
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.*;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }

    public int getX() {
        return a + point.getX();
    }
    public void setX(int x) {
        this.a = x;
    }

    public int getY() {
        return b + point.getY();
    }
    public void setY(int y) {
        this.b = y;
    }

    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    public void move(int dx, int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=26	-93	=17	-37	+*	=28	-128	=124	-1	+R	=1	-1	+t	=2	-3	=1	-3	+Li	=1	+e	=103	-92	=118	-544	=94	-27	=120	-27	=270	-50	=54	-88	=118	-46	=59	-88	=189	-4	=3
//END OF FILE

//SESSION END

//SESSION START
83,Wed Jul 09 03:49:38 EDT 2025,-4
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface{
	int x, y, width, height;
	public AScalableRectangle(int x, int y, int width, int height) {
		this.x = x;
		this.y = y;
		this.width = width;
		this.height = height;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/100;
		height = (height*percentage)/100;		
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=320	-4	+x	=6	-4	+y	=6	-4	+w	=10	-1	=1	-2	=11	+this.	=4	-4	+x	=4	+this.	=4	-4	+y	=4	+this.	=8	-4	+w	=8	+this.	=9	-1	=1	-2	=373
//END OF FILE

//SESSION END

//SESSION START
84,Wed Jul 09 03:53:29 EDT 2025,-3339
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import util.annotations.*;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	-45	=24	-93	+*	=687
//END OF FILE
//START OF FILE: main/Assignment2.java
package main;

import bus.uigen.*;
import mp.shapes.*;
import mp.bridge.*;

public class Assignment2 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        RotatingLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }
    public static void main(String[] args) throws InterruptedException {
    	BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=851	-1	=187
//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.*;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=110	-93	+*	=3235
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;

import util.annotations.Visible;
import mp.shapes.RotatingLine;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotatingLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotatingLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotatingLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }

}

(DIFF_FROM_PREVIOUS_FILE)
=531	-303	=315	-110	=51	-204	=2	-247	=3
//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.*;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=363	-147	=30	-95	=48
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.*;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) { 
    	this.x = x; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-93	+*	=670
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.*;
import main.StaticFactoryClass;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=44	-63	+*	=31	-30	=438	-3	+leg	=742
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
import mp.shapes.RotatingLine;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle{
    RotatingLine getLeftLine();
    RotatingLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=201	-47	=101	-35	=100
//END OF FILE
//START OF FILE: main/ConsoleSceneView.java
//@#$DELETED FILE&^%$
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import util.annotations.*;
import tags301.Comp301Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=43	-93	+*	=294
//END OF FILE
//START OF FILE: main/ConsoleSceneViewIm.java
//@#$DELETED FILE&^%$
//END OF FILE

//SESSION END

//SESSION START
85,Wed Jul 09 03:56:51 EDT 2025,-347
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge {
    int RIGHT_LINE_X = 950;
    int LINE_TOP_Y = 0;
    int LINE_HEIGHT = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangle rectangle;
    public Gorge(int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(LINE_HEIGHT);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(LINE_TOP_Y);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(LINE_HEIGHT);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(RIGHT_LINE_X);
        rightLine.setY(LINE_TOP_Y);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, RIGHT_LINE_X - x, lower);
    }

    public RotateLine getLeftLine() {
    	return leftLine;
    }
    public RotateLine getRightLine(){
    	return rightLine;
    }
    public AScalableRectangle getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=208	-3	+e	=24	-3	+e	=635	-3	+e	=65	-3	+e	=127
//END OF FILE
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.*;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape {
    String getText();
    void setText(String t);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-93	+*	=272
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.*;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String fn) { 
    	this.fn = fn; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int x) {
    	this.x = x; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-93	+*	=668
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;


import util.annotations.*;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape {
    private String text = "Grail";
    private int x, y;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return x;
    }
    @Override
    public void setX(int x) { 
    	this.x = x; }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int y) { 
    	this.y = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=45	-63	+*	=600
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.*;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-4	+*	=29	-90	=242
//END OF FILE

//SESSION END

//SESSION START
86,Wed Jul 09 04:00:23 EDT 2025,-476
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import mp.shapes.*;
import util.annotations.*;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=26	-166	=10	-101	+*	=26	-4	+*	=172	-3	+e	=144	-3	+e	=78	-3	+e	=162	-1	=3
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;
import util.annotations.*;
import tags301.Comp301Tags;
import mp.shapes.*;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle{
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	-1	=24	-93	+*	=47	-12	+*	=117	-3	+e	=29	-3	+e	=54
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.*;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine {
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
    int getWidth();
    int getHeight();
    double getRadius();
    void setRadius(double r);
    double getAngle();
    void setAngle(double angle);
    void rotate(int units);
    void move(int x, int y);
}
(DIFF_FROM_PREVIOUS_FILE)
=44	-4	+*	=27	-89	=403
//END OF FILE

//SESSION END

//SESSION START
87,Wed Jul 09 04:04:52 EDT 2025,-92
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.*;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
}
(DIFF_FROM_PREVIOUS_FILE)
=44	-4	+*	=27	-89	=243
//END OF FILE

//SESSION END

//SESSION START
88,Wed Jul 09 04:08:01 EDT 2025,-11
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.*;
import tags301.Comp301Tags;
import util.annotations.*;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    @Tags(Comp301Tags.AVATAR)
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}

(DIFF_FROM_PREVIOUS_FILE)
=51	-5	+*	=55	-37	=2131	+%0A    @Tags(Comp301Tags.AVATAR)	=1067
//END OF FILE

//SESSION END

//SESSION START
90,Wed Jul 09 04:11:39 EDT 2025,212
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private AvatarImpl cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(AvatarImpl avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-1	+ArthurHead;%0Aimport mp.bridge.LancelotHead;%0Aimport mp.bridge.RobinHead;%0Aimport mp.bridge.GalahadHead;%0Aimport mp.bridge.GuardHead;%0Aimport mp.bridge.Avatar;%0Aimport mp.bridge.AvatarImpl;%0Aimport mp.bridge.BridgeScene	=19	-1	+Gorge	=54	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport mp.shapes.AScalableRectangle	=514	+Impl	=1622	-30	=27	+Impl	=925	-50	=1	-59
//END OF FILE

//SESSION END

//SESSION START
91,Wed Jul 09 04:13:40 EDT 2025,-8
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
}
(DIFF_FROM_PREVIOUS_FILE)
=964	-4	=1649	-4	=926
//END OF FILE

//SESSION END

//SESSION START
92,Wed Jul 09 04:15:00 EDT 2025,52
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
}
(DIFF_FROM_PREVIOUS_FILE)
=3538	+    public boolean getOccupied() %7Breturn Occupied;%7D%0A	=1
//END OF FILE

//SESSION END

//SESSION START
93,Wed Jul 09 04:16:17 EDT 2025,56
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=3590	+    public boolean getKnightTurn() %7Breturn KnightTurn;%7D%0A	=1
//END OF FILE

//SESSION END

//SESSION START
94,Wed Jul 09 04:17:28 EDT 2025,-402
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.*;
import tags301.Comp301Tags;
import util.annotations.*;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea, guardArea;
    private boolean KnightTurn, Occupied = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-211	+*	=19	-5	+*	=54	-130	+*	=561	-32	+,	=42	+, Occupied	=236	-38	=2288
//END OF FILE

//SESSION END

//SESSION START
95,Wed Jul 09 04:18:04 EDT 2025,59
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.*;
import tags301.Comp301Tags;
import util.annotations.*;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=668	-1	+;%0A    private AScalableRectangle	=42	-10	=236	+;%0A    private boolean Occupied = false	=2288
//END OF FILE

//SESSION END

//SESSION START
96,Wed Jul 09 04:18:22 EDT 2025,343
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-1	+ArthurHead;%0Aimport mp.bridge.LancelotHead;%0Aimport mp.bridge.RobinHead;%0Aimport mp.bridge.GalahadHead;%0Aimport mp.bridge.GuardHead;%0Aimport mp.bridge.Avatar;%0Aimport mp.bridge.AvatarImpl;%0Aimport mp.bridge.BridgeScene	=19	-1	+Gorge	=54	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport mp.shapes.AScalableRectangle	=3197
//END OF FILE

//SESSION END

//SESSION START
97,Wed Jul 09 04:18:37 EDT 2025,-210
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.*;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-211	+*	=3405
//END OF FILE

//SESSION END

//SESSION START
98,Wed Jul 09 04:18:53 EDT 2025,210
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int some_x = 10;
    public static final int some_y = 50;
    public static final int lance_const = 8;
    public static final int robin_const = 15;
    public static final int gal_const = 22;
    public static final int guard_const = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangle knightArea;
    private AScalableRectangle guardArea;
    private boolean KnightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean Occupied = false;
    private static final int Gorge_X = 750;
    private static int Gorge_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(some_x, some_y);
      lancelot.move(some_x*lance_const, some_y);
      robin.move(some_x*robin_const, some_y);
      galahad.move(some_x*gal_const,some_y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(Gorge_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!KnightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		Occupied = false;
    	}
    }
    public void failed(){
    	if(Occupied) {
    		if(!KnightTurn) {
    	cur.getHead().setX(Gorge_X);
    	cur.getHead().setY(Gorge_Y);
    	Gorge_Y += diff;
    	Occupied = !Occupied;}
    		else {guard.getHead().setX(Gorge_X);
        	guard.getHead().setY(Gorge_Y);
        	Gorge_Y += diff;}
    		}
    }
    public void approach(Avatar avatar){
    	if(!Occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	Occupied = true;
    	cur = avatar;
    }
    public void say(String s){
    	if(Occupied){
    		if(!KnightTurn){guard.getStringShape().setText(s);KnightTurn= !KnightTurn;} 
    		else {cur.getStringShape().setText(s);KnightTurn = !KnightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangle getKnightArea() {
        return knightArea;
    }
    public AScalableRectangle getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return Occupied;}
    public boolean getKnightTurn() {return KnightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-1	+ArthurHead;%0Aimport mp.bridge.LancelotHead;%0Aimport mp.bridge.RobinHead;%0Aimport mp.bridge.GalahadHead;%0Aimport mp.bridge.GuardHead;%0Aimport mp.bridge.Avatar;%0Aimport mp.bridge.AvatarImpl;%0Aimport mp.bridge.BridgeScene	=3405
//END OF FILE

//SESSION END

//SESSION START
100,Wed Jul 09 05:00:04 EDT 2025,2154
//START OF FILE: mp/shapes/Locatable.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface Locatable {
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
}

//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape {
    private String text = "Grail";
    private int a, b;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return a;
    }
    @Override
    public void setX(final int x) { 
    	a = x; 
    }
    @Override
    public int getY() { 
    	return b; 
    }
    @Override
    public void setY(final int y) { 
    	b = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+import util.annotations.Tags;	=25	-1	+StructurePattern;%0Aimport util.annotations.StructurePatternNames	=2	+import tags301.Comp301Tags;%0A@Tags(Comp301Tags.LOCATABLE)	=159	-1	+a	=2	-1	+b	=230	-1	+a	=43	+final 	=15	-6	+a	=5	+ %0A   	=54	-1	+b	=44	+final 	=15	-6	+b	=15
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
}
(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=270
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=376	-1	+ile	=10	-5	=6	-1	+ile	=109	-1	+n	=10	-5	=4	-1	+n	=109	-1	+n	=10	-5	=4	-1	+n	=11
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;

public interface AScalableRectangleInterface extends BoundedShape{
	public int getX();
	public int getY();
	public int getWidth();	
	public int getHeight();	
	public void setHeight(int newVal);
	public void setWidth(int newVal);
	public void scale(int percentage);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	-171	=46	+extends BoundedShape	=202
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle{
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=43	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=47	-1	+RotateLine	=202
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface{
	int x, y, width, height;
	int percentConversion = 100;
	public AScalableRectangle(int x, int y, int width, int height) {
		this.x = x;
		this.y = y;
		this.width = width;
		this.height = height;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int a) {
		x = a;
	}
	@Override
	public void setY(int b) {
		y = b;
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=290	+int percentConversion = 100;%0A%09	=458	-3	+percentConversion	=33	-3	+percentConversion	=3	+%0A%09%7D%0A%09@Override%0A%09public void setX(int a) %7B%0A%09%09x = a;%0A%09%7D%0A%09@Override%0A%09public void setY(int b) %7B%0A%09%09y = b;	=5
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape{
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
    int getWidth();
    int getHeight();
    double getRadius();
    void setRadius(double r);
    double getAngle();
    void setAngle(double angle);
    void rotate(int units);
    void move(int x, int y);
}
(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=49	-1	+B	=1	-3	+UNDED_SH	=1	-2	+P	=86	+extends BoundedShape	=287
//END OF FILE
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape extends Locatable{
    String getText();
    void setText(String t);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport mp.shapes.Locatable	=142	+extends Locatable	=130
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) {
    	x = n; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=376	-1	+ile	=10	-5	=6	-1	+ile	=109	-1	+n	=9	-5	=4	-1	+n	=108	-1	+n	=10	-5	=4	-1	+n	=11
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=43	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=393	-1	+ile	=10	-5	=6	-1	+ile	=109	-1	+n	=10	-5	=4	-1	+n	=109	-1	+n	=10	-5	=4	-1	+n	=11
//END OF FILE
//START OF FILE: mp/shapes/BoundedShape.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape {
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
    int getWidth();
    int getHeight();
}
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle {
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=36	-1	+RotateLine;%0Aimport mp.shapes.RotatingLine	=26	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=562
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=271
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(int dx, int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=44	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=1214
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }

    public int getX() {
        return a + point.getX();
    }
    public void setX(final int x) {
        a = x;
    }

    public int getY() {
        return b + point.getY();
    }
    public void setY(final int y) {
        b = y;
    }

    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(final double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    public void move(final int dx, final int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=43	-1	+Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames	=468	+final 	=17	-5	=98	+final 	=17	-5	=245	+final 	=168	+final 	=105	+final 	=92	+final 	=7	+ final	=63
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=26	+tags301.Comp301Tags;%0Aimport 	=17	-1	+Tags	=9	+u	=1	+il.	=1	-1	+nnotation	=1	-3	=1	-2	+StructurePattern;%0Ai	=2	-4	+ort util.	=1	-1	+nnotations.StructurePatternName	=267
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=524	-1	+ile	=10	-5	=6	-1	+ile	=109	-1	+n	=10	-5	=4	-1	+n	=109	-1	+n	=10	-5	=4	-1	+n	=11
//END OF FILE

//SESSION END

//SESSION START
101,Wed Jul 09 05:22:37 EDT 2025,493
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge {
    int RIGHT_LINE_X = 950;
    int LINE_TOP_Y = 0;
    int LINE_HEIGHT = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangle rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(LINE_HEIGHT);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(LINE_TOP_Y);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(LINE_HEIGHT);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(RIGHT_LINE_X);
        rightLine.setY(LINE_TOP_Y);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, RIGHT_LINE_X - x, lower);
    }

    public RotateLine getLL() {
    	return leftLine;
    }
    public RotateLine getRL(){
    	return rightLine;
    }
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=301	+final 	=578	-3	=1	-3	=59	-4	=1	-3	=61	+eInterfac	=49
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(final int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(final int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=521	+final 	=16	-0	=119	+final 	=104	-0	=21	+final 	=31
//END OF FILE
//START OF FILE: main/Assignment2.java
package main;

import bus.uigen.OEFrame;
import bus.uigen.ObjectEditor;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import mp.bridge.BridgeScene;

public class Assignment2 {
    public static final int SOME_RAD = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int D = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        final RotateLine line = new RotatingLine();
        line.setRadius(SOME_RAD);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        final OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(D, D);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }
    public static void main(String[] args) throws InterruptedException {
    	final BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=32	-1	+OEFrame;%0Aimport bus.uigen.ObjectEditor;%0Aimport mp.shapes.RotateLine	=19	-1	+RotatingLine	=19	-1	+BridgeScene	=411	+final 	=5	-3	+e	=166	+final 	=266	-0	=5	+final 	=109
//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X = 10;
    public static final int SOME_Y = 50;
    public static final int L_CONST = 8;
    public static final int R_CONST = 15;
    public static final int GAL_CONST = 22;
    public static final int GUARD_CONST = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X = 750;
    private static int GORGE_Y = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X, SOME_Y);
      lancelot.move(SOME_X*L_CONST, SOME_Y);
      robin.move(SOME_X*R_CONST, SOME_Y);
      galahad.move(SOME_X*GAL_CONST,SOME_Y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(GORGE_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		occupied = false;
    	}
    }
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_X);
    	cur.getHead().setY(GORGE_Y);
    	GORGE_Y += diff;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X);
        	guard.getHead().setY(GORGE_Y);
        	GORGE_Y += diff;}
    		}
    }
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	occupied = true;
    	cur = avatar;
    }
    public void say(final String s){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(s);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(s);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=452	+import mp.shapes.AScalableRectangleInterface;%0A	=234	-4	+SOME	=1	-1	+X	=35	-4	+SOME	=1	-1	+Y	=35	-5	+L	=1	-5	+CONST	=34	-5	+R	=1	-5	+CONST	=35	-3	+GAL	=1	-5	+CONST	=35	-5	+GUARD	=1	-5	+CONST	=86	+Interface	=43	+Interface	=32	-1	+k	=267	-1	+o	=47	-4	+ORGE	=34	-4	+ORGE	=323	-4	+SOME	=1	-1	+X	=2	-4	+SOME	=1	-1	+Y	=23	-4	+SOME	=1	-1	+X	=1	-5	+L	=1	-5	+CONST	=2	-4	+SOME	=1	-1	+Y	=20	-4	+SOME	=1	-1	+X	=1	-5	+R	=1	-5	+CONST	=2	-4	+SOME	=1	-1	+Y	=22	-4	+SOME	=1	-1	+X	=1	-3	+GAL	=1	-5	+CONST	=1	-4	+SOME	=1	-1	+Y	=62	-4	+ORGE	=210	-1	+k	=52	-1	+o	=64	-1	+o	=21	-1	+k	=38	-4	+ORGE	=30	-4	+ORGE	=11	-4	+ORGE	=17	-1	+o	=11	-1	+o	=44	-4	+ORGE	=36	-4	+ORGE	=15	-4	+ORGE	=52	+final 	=25	-1	+o	=47	-1	+o	=61	+final 	=19	-1	+o	=20	-1	+k	=45	-1	+k	=12	-1	+k	=57	-1	+k	=13	-1	+k	=449	+Interface	=81	+Interface	=135	-1	+o	=53	-1	+k	=13
//END OF FILE
//START OF FILE: mp/shapes/BoundedShape.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape extends Locatable{
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
    int getWidth();
    int getHeight();
}
(DIFF_FROM_PREVIOUS_FILE)
=283	+extends Locatable	=132
//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

public class APolarPoint implements Point {
	double radius, angle;
	public APolarPoint(double theRadius, double theAngle) {
		radius = theRadius;
		angle = theAngle;
	}
	public APolarPoint(int theX, int theY) {
		radius = Math.sqrt(theX*theX + theY*theY);
		angle = Math.atan((double) theY/theX);
	}
	public int getX() { return (int) (radius*Math.cos(angle)); }
	public int getY() { return (int) (radius*Math.sin(angle)); }
	public double getAngle() { return angle; } 
	public double getRadius() { return radius;}	
}

(DIFF_FROM_PREVIOUS_FILE)
=321	-11	=61	-11	=63	-11	=45	-11	=45	-0	=1	+%0A
//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.Angle;
import mp.bridge.VShape;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER)
public class StaticFactoryClass{
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=31	-1	+Angle;%0Aimport mp.bridge.VShape;%0Aimport mp.bridge.BridgeScene	=78	-4	=1	+BSE	=1	-1	+VABLE	=1	+BRIDGE_S	=1	-1	+ENE_P	=1	-2	+INTER	=318
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=505	+final 	=291
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public interface AScalableRectangleInterface extends BoundedShape{
	public int getX();
	public int getY();
	public int getWidth();	
	public int getHeight();	
	public void setHeight(int newVal);
	public void setWidth(int newVal);
	public void scale(int percentage);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.Tags;%0A@Tags(Comp301Tags.BOUNDED_SHAPE)%0A@StructurePattern(%22Rectangle Pattern%22)	=268
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape head) {
        this.head = head;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=479	+final 	=609	+final 	=7	+ final	=256
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends Locatable{
    String getImageFileName();
    void setImageFileName(String fn);
    int getX();
    void setX(int x);
    int getY();
    void setY(int y);
}

(DIFF_FROM_PREVIOUS_FILE)
=26	+mp.shapes.Locatable;%0Aimport 	=254	+e extends Locatabl	=150
//END OF FILE

//SESSION END

//SESSION START
102,Wed Jul 09 05:24:52 EDT 2025,-20
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	this.fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int a) {
    	this.x = a; 
  
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int b) { 
    	
    	this.y = b; 
    	
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=533	-1	+ile	=21	-1	+ile	=95	-1	+a	=18	-1	+a	=111	-1	+b	=25	-1	+b	=17
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=497	-3	=25	-3	=841
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface{
	int x, y, width, height;
	int percentConversion = 100;
	public AScalableRectangle(int a, int b, int w, int h) {
		this.x = a;
		this.y = b;
		this.width = w;
		this.height = h;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int a) {
		x = a;
	}
	@Override
	public void setY(int b) {
		y = b;
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=350	-1	+a	=6	-1	+b	=7	-4	=7	-5	=15	-1	+a	=13	-1	+b	=18	-4	=19	-5	=496
//END OF FILE

//SESSION END

//SESSION START
103,Wed Jul 09 05:29:48 EDT 2025,-5
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X = 10;
    public static final int SOME_Y = 50;
    public static final int L_CONST = 8;
    public static final int R_CONST = 15;
    public static final int GAL_CONST = 22;
    public static final int GUARD_CONST = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X = 750;
    private static int gorgey = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X, SOME_Y);
      lancelot.move(SOME_X*L_CONST, SOME_Y);
      robin.move(SOME_X*R_CONST, SOME_Y);
      galahad.move(SOME_X*GAL_CONST,SOME_Y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(GORGE_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		occupied = false;
    	}
    }
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_X);
    	cur.getHead().setY(gorgey);
    	gorgey += diff;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X);
        	guard.getHead().setY(gorgey);
        	gorgey += diff;}
    		}
    }
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	occupied = true;
    	cur = avatar;
    }
    public void say(final String s){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(s);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(s);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=1484	-7	+gorgey	=955	-7	+gorgey	=8	-7	+gorgey	=111	-7	+gorgey	=12	-7	+gorgey	=1120
//END OF FILE

//SESSION END

//SESSION START
104,Wed Jul 09 05:31:19 EDT 2025,-15
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge {
    int rightlinex = 950;
    int linetopy = 0;
    int lineheight = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangle rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(lineheight);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(linetopy);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(lineheight);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(rightlinex);
        rightLine.setY(linetopy);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, rightlinex - x, lower);
    }

    public RotateLine getLL() {
    	return leftLine;
    }
    public RotateLine getRL(){
    	return rightLine;
    }
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=49	-12	+rightlinex	=16	-10	+linetopy	=14	-11	+lineheight	=270	-11	+lineheight	=91	-10	+linetopy	=111	-11	+lineheight	=65	-12	+rightlinex	=26	-10	+linetopy	=97	-12	+rightlinex	=227
//END OF FILE

//SESSION END

//SESSION START
105,Wed Jul 09 05:33:36 EDT 2025,9
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge {
    int rightlinex = 950;
    int linetopy = 0;
    int lineheight = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(lineheight);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(linetopy);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(lineheight);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(rightlinex);
        rightLine.setY(linetopy);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, rightlinex - x, lower);
    }

    public RotateLine getLL() {
    	return leftLine;
    }
    public RotateLine getRL(){
    	return rightLine;
    }
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=267	+Interface	=783
//END OF FILE

//SESSION END

//SESSION START
106,Wed Jul 09 05:35:58 EDT 2025,171
//START OF FILE: mp/shapes/moveable.java
package mp.shapes;

public interface moveable {
public void move(int dx, int dy);
}

//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends moveable{
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.moveable;%0Aimport 	=125	+extends moveable	=137
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.moveable;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends moveable{
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=194	+%0Aimport mp.shapes.moveable;	=103	+le extends moveab	=98
//END OF FILE

//SESSION END

//SESSION START
107,Wed Jul 09 05:36:51 EDT 2025,0
//START OF FILE: mp/shapes/Moveable.java
package mp.shapes;

public interface Moveable {
public void move(int dx, int dy);
}

//END OF FILE
//START OF FILE: mp/shapes/moveable.java
//@#$DELETED FILE&^%$
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable{
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=156	-1	+M	=149	-1	+M	=144
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable{
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=212	-1	+M	=122	-1	+M	=103
//END OF FILE

//SESSION END

//SESSION START
108,Wed Jul 09 05:40:36 EDT 2025,415
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape, Locatable {
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	this.fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int a) {
    	this.x = a; 
  
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int b) { 
    	
    	this.y = b; 
    	
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.Locatable;%0Aimport 	=148	+e, Locatabl	=536
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape, Locatable {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) {
    	x = n; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.Locatable;%0Aimport 	=146	+e, Locatabl	=502
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape, Locatable {
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(final int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(final int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=145	+mp.shapes.Locatable;%0Aimport 	=147	+e, Locatabl	=538
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape, Locatable {
    private String text = "Grail";
    private int a, b;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return a;
    }
    @Override
    public void setX(final int x) { 
    	a = x; 
    }
    @Override
    public int getY() { 
    	return b; 
    }
    @Override
    public void setY(final int y) { 
    	b = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.Locatable;%0Aimport 	=153	+e, Locatabl	=501
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle, Locatable, Moveable {
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=36	+Locatable;%0Aimport mp.shapes.Moveable;%0Aimport mp.shapes.	=304	+le, Locatable, Moveab	=418
//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

public class APolarPoint implements Point{
	double radius, angle;
	public APolarPoint(double theRadius, double theAngle) {
		radius = theRadius;
		angle = theAngle;
	}
	public APolarPoint(int theX, int theY) {
		radius = Math.sqrt(theX*theX + theY*theY);
		angle = Math.atan((double) theY/theX);
	}
	public int getX() { return (int) (radius*Math.cos(angle)); }
	public int getY() { return (int) (radius*Math.sin(angle)); }
	public double getAngle() { return angle; } 
	public double getRadius() { return radius;}	
}

(DIFF_FROM_PREVIOUS_FILE)
=61	-1	=475
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape, Locatable {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.Locatable;%0Aimport 	=145	+e, Locatabl	=511
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Locatable, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=178	+mp.shapes.Locatable;%0Aimport mp.shapes.Moveable;%0Aimport 	=142	+,	=1	+Locatable, Moveable	=1042
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine, Locatable, Moveable{
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }

    public int getX() {
        return a + point.getX();
    }
    public void setX(final int x) {
        a = x;
    }

    public int getY() {
        return b + point.getY();
    }
    public void setY(final int y) {
        b = y;
    }

    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(final double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    public void move(final int dx, final int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=298	+,	=1	+Locatable, Moveable	=1159
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, Locatable{
	int x, y, width, height;
	int percentConversion = 100;
	public AScalableRectangle(int a, int b, int w, int h) {
		this.x = a;
		this.y = b;
		this.width = w;
		this.height = h;
	}
	public int getX() {return x;}
	public int getY() {return y;}
	public int getWidth() {return width;}	
	public int getHeight() { return height;}	
	public void setHeight(int newVal) {height = newVal;}
	public void setWidth(int newVal) {width = newVal;}
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int a) {
		x = a;
	}
	@Override
	public void setY(int b) {
		y = b;
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=261	+, Locatable	=674
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape, Locatable {
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.Locatable;%0Aimport 	=145	+e, Locatabl	=516
//END OF FILE

//SESSION END

//SESSION START
109,Wed Jul 09 05:46:29 EDT 2025,134
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle, Moveable {
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=399	-11	=424
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=377	-11	=1050
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine, Locatable, Moveable{
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return a + point.getX();
    }
    @Override
    public void setX(final int x) {
        a = x;
    }
    @Override
    public int getY() {
        return b + point.getY();
    }
    @Override
    public void setY(final int y) {
        b = y;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(final double r) {
        point = new APolarPoint(r, point.getAngle());
    }

    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }
    @Override
    public void move(final int dx, final int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=539	+    @Override	=1	-0	=63	+    @Override%0A	=57	+    @Override	=1	-0	=63	+    @Override%0A	=57	+    @Override	=593	+    @Override	=104
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, Locatable{
	int x, y, width, height;
	int percentConversion = 100;
	public AScalableRectangle(int a, int b, int w, int h) {
		this.x = a;
		this.y = b;
		this.width = w;
		this.height = h;
	}
	@Override
	public int getX() {return x;}
	@Override
	public int getY() {return y;}
	@Override
	public int getWidth() {return width;}	
	@Override
	public int getHeight() { return height;}
	@Override
	public void setHeight(int newVal) {height = newVal;}
	@Override
	public void setWidth(int newVal) {width = newVal;}
	@Override
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int a) {
		x = a;
	}
	@Override
	public void setY(int b) {
		y = b;
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=456	+@Override%0A%09	=29	+%0A%09@Override	=33	+@Override%0A%09	=40	+@Override%0A%09	=40	+%0A	=1	+@Override	=56	+@Override%0A%09	=50	+%0A%09@Override	=241
//END OF FILE

//SESSION END

//SESSION START
110,Wed Jul 09 05:50:23 EDT 2025,410
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

public class APolarPoint implements Point, Locatable, PolarPointInterface{
	double radius, angle;
	public APolarPoint(double theRadius, double theAngle) {
		radius = theRadius;
		angle = theAngle;
	}
	public APolarPoint(int theX, int theY) {
		radius = Math.sqrt(theX*theX + theY*theY);
		angle = Math.atan((double) theY/theX);
	}
	@Override
	public int getX() { return (int) (radius*Math.cos(angle)); }
	@Override
	public int getY() { return (int) (radius*Math.sin(angle)); }
	@Override
	public double getAngle() { return angle; } 
	@Override
	public double getRadius() { return radius;}
	@Override
	public void setX(int x) {
	}
	@Override
	public void setY(int y) {
	}	
}

(DIFF_FROM_PREVIOUS_FILE)
=61	+, Locatable, PolarPointInterface	=2	-0	=257	+@Override%0A%09	=62	+@Override%0A%09	=62	+@Override%0A%09	=44	+%09@Override%0A	=43	+%7D%0A%09@Override%0A%09public void setX(int x) %7B%0A%09%7D%0A%09@Override%0A%09public void setY(int y) %7B%0A%09	=5
//END OF FILE
//START OF FILE: mp/shapes/PolarPointInterface.java
package mp.shapes;

public interface PolarPointInterface {
	public int getX();
	public int getY();
	public double getAngle();
	public double getRadius();
	public void setX(int x) ;
	public void setY(int y);
}

//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine, Locatable, Moveable, PolarPointInterface{
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return a + point.getX();
    }
    @Override
    public void setX(final int x) {
        a = x;
    }
    @Override
    public int getY() {
        return b + point.getY();
    }
    @Override
    public void setY(final int y) {
        b = y;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    @Override
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(final double r) {
        point = new APolarPoint(r, point.getAngle());
    }
    @Override
    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }
    @Override
    public void move(final int dx, final int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=319	+, PolarPointInterface	=662	+@Override	=177	+    @Override	=400
//END OF FILE

//SESSION END

//SESSION START
111,Wed Jul 09 05:54:10 EDT 2025,-346
//START OF FILE: mp/shapes/BoundedShape.java
package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape extends Locatable{
    int getWidth();
    int getHeight();
}
(DIFF_FROM_PREVIOUS_FILE)
=313	-88	=31
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public interface AScalableRectangleInterface extends BoundedShape{
	public void scale(int percentage);
}

(DIFF_FROM_PREVIOUS_FILE)
=266	-162	=30
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, Locatable{
	int x, y, width, height;
	int percentConversion = 100;
	public AScalableRectangle(int a, int b, int w, int h) {
		this.x = a;
		this.y = b;
		this.width = w;
		this.height = h;
	}
	@Override
	public int getX() {return x;}
	@Override
	public int getY() {return y;}
	@Override
	public int getWidth() {return width;}	
	@Override
	public int getHeight() { return height;}

	public void setHeight(int newVal) {height = newVal;}

	public void setWidth(int newVal) {width = newVal;}
	@Override
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int a) {
		x = a;
	}
	@Override
	public void setY(int b) {
		y = b;
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=643	-10	=55	-10	=304
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends Locatable{
    String getImageFileName();
    void setImageFileName(String fn);
}

(DIFF_FROM_PREVIOUS_FILE)
=398	-76	=2
//END OF FILE

//SESSION END

//SESSION START
112,Wed Jul 09 05:55:41 EDT 2025,213
//START OF FILE: main/RunSS25A2Tests.java
package main;

import grader.basics.execution.BasicProjectExecution;
import gradingTools.comp301ss24.assignment2.SS24Assignment2Suite;
import trace.grader.basics.GraderBasicsTraceUtility;
public class RunSS25A2Tests {
	 private static final int MAX_PRINTED_TRACES = 600;
		 private static final int MAX_TRACES         = 2000;
		 private static final int PROCESS_TIMEOUT_S  = 5;
		public static void main(String[] args) {
			// if you set this to false, grader steps will not be traced
			GraderBasicsTraceUtility.setTracerShowInfo(true);	
			// if you set this to false, all grader steps will be traced,
			// not just the ones that failed		
			GraderBasicsTraceUtility.setBufferTracedMessages(true);
			// Change this number if a test trace gets longer than 600 and is clipped
			GraderBasicsTraceUtility.setMaxPrintedTraces(MAX_PRINTED_TRACES);
			// Change this number if all traces together are longer than 2000
			GraderBasicsTraceUtility.setMaxTraces(MAX_TRACES);
			// Change this number if your process times out prematurely
			BasicProjectExecution.setProcessTimeOut(PROCESS_TIMEOUT_S);
			// You need to always call such a method
		SS24Assignment2Suite.main(args);
	}
}

(DIFF_FROM_PREVIOUS_FILE)
=219	+ private static final int MAX_PRINTED_TRACES = 600;%0A%09%09 private static final int MAX_TRACES         = 2000;%0A%09%09 private static final int PROCESS_TIMEOUT_S  = 5;%0A%09%09	=41	+%09	=65	+%09	=49	+%09	=1	+%09	=66	+%09	=32	+%09%09	=1	+%09	=60	+%09	=76	+%09	=45	-3	+MAX_PRINTED_TRACES	=3	+%09	=70	+%09	=38	-4	+MAX_TRACES	=3	+%09	=64	+%09	=40	-1	+PROCESS_TIMEOUT_S	=3	+%09	=83
//END OF FILE

//SESSION END

//SESSION START
113,Wed Jul 09 05:59:17 EDT 2025,-269
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public interface StringShape extends Locatable{
    String getText();
    void setText(String t);
}

(DIFF_FROM_PREVIOUS_FILE)
=376	-76	=2
//END OF FILE
//START OF FILE: mp/shapes/PolarPointInterface.java
package mp.shapes;

public interface PolarPointInterface extends Locatable{
	public double getAngle();
	public double getRadius();
}

(DIFF_FROM_PREVIOUS_FILE)
=57	-12	+ex	=1	-2	=1	+nds Loca	=1	-8	+a	=2	-8	=1	-5	+%7B	=53	-53	=5
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape{
    double getRadius();
    void setRadius(double r);
    double getAngle();
    void setAngle(double angle);
    void rotate(int units);
    void move(int x, int y);
}
(DIFF_FROM_PREVIOUS_FILE)
=310	-117	=164
//END OF FILE

//SESSION END

//SESSION START
114,Wed Jul 09 06:01:20 EDT 2025,-123
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape{
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	this.fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    public void setX(int a) {
    	this.x = a; 
  
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int b) { 
    	
    	this.y = b; 
    	
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=323	-12	=534
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) {
    	x = n; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=321	-11	=501
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape{
    private String fn = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(final int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(final int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=321	-12	=536
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape{
    private String text = "Grail";
    private int a, b;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String t) { 
    	text = t; 
    }
    @Override
    public int getX() { 
    	return a;
    }
    @Override
    public void setX(final int x) { 
    	a = x; 
    }
    @Override
    public int getY() { 
    	return b; 
    }
    @Override
    public void setY(final int y) { 
    	b = y; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=328	-12	=499
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle{
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=397	-11	=415
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }

    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=320	-11	=510
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine, Moveable, PolarPointInterface{
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return a + point.getX();
    }
    @Override
    public void setX(final int x) {
        a = x;
    }
    @Override
    public int getY() {
        return b + point.getY();
    }
    @Override
    public void setY(final int y) {
        b = y;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }

    public int getHeight() {
        return point.getY();
    }
    @Override
    public double getRadius() {
        return point.getRadius();
    }
    public void setRadius(final double r) {
        point = new APolarPoint(r, point.getAngle());
    }
    @Override
    public double getAngle() {
        return point.getAngle();
    }
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }
    @Override
    public void move(final int dx, final int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=300	-11	=1290
//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable{
    RotateLine getLeftLine();
    RotateLine getRightLine();
}

(DIFF_FROM_PREVIOUS_FILE)
=406	-31	=2
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape{
    private String fn = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=320	-12	=514
//END OF FILE

//SESSION END

//SESSION START
115,Wed Jul 09 06:03:36 EDT 2025,-47
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape{
    void setRadius(double r);
    void setAngle(double angle);
    void rotate(int units);
    void move(int x, int y);
}
(DIFF_FROM_PREVIOUS_FILE)
=310	-24	=23	-23	=94
//END OF FILE

//SESSION END

//SESSION START
116,Wed Jul 09 06:10:21 EDT 2025,-20
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge{
    int rightlinex = 950;
    int linetopy = 0;
    int lineheight = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(lineheight);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(linetopy);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(lineheight);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(rightlinex);
        rightLine.setY(linetopy);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, rightlinex - x, lower);
    }

    public RotateLine getLL() {
    	return leftLine;
    }
    public RotateLine getRL(){
    	return rightLine;
    }
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=38	-1	=1020
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape, Moveable{
    void setRadius(double r);
    void setAngle(double angle);
    void rotate(int units);
}
(DIFF_FROM_PREVIOUS_FILE)
=304	+, Moveable	=90	-29	=4
//END OF FILE

//SESSION END

//SESSION START
118,Wed Jul 09 06:28:12 EDT 2025,962
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge implements GetRect{
    int rightlinex = 950;
    int linetopy = 0;
    int lineheight = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(lineheight);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(linetopy);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(lineheight);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(rightlinex);
        rightLine.setY(linetopy);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, rightlinex - x, lower);
    }
    @Override
    public RotateLine getLeftLine() {
    	return leftLine;
    }
    @Override
    public RotateLine getRightLine(){
    	return rightLine;
    }
    @Override
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=38	+ implements GetRect	=813	+    @Override	=27	+eft	=1	+ine	=37	+@Override%0A    	=22	+ight	=1	+ine	=32	+%0A    @Override	=87
//END OF FILE
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape{
    private String fn = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	this.fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int a) {
    	this.x = a; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int b) { 
    	this.y = b; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=661	+@Override%0A    	=42	-3	=118	-6	=12	-6	=9
//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X = 10;
    public static final int SOME_Y = 50;
    public static final int L_CONST = 8;
    public static final int R_CONST = 15;
    public static final int GAL_CONST = 22;
    public static final int GUARD_CONST = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X = 750;
    private static int gorgey = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X, SOME_Y);
      lancelot.move(SOME_X*L_CONST, SOME_Y);
      robin.move(SOME_X*R_CONST, SOME_Y);
      galahad.move(SOME_X*GAL_CONST,SOME_Y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(GORGE_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		occupied = false;
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_X);
    	cur.getHead().setY(gorgey);
    	gorgey += diff;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X);
        	guard.getHead().setY(gorgey);
        	gorgey += diff;}
    		}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	occupied = true;
    	cur = avatar;
    }
    @Override
    public void say(final String s){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(s);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(s);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
}
(DIFF_FROM_PREVIOUS_FILE)
=2202	+@Override%0A    	=114	+%0A    @Override	=313	+@Override%0A    	=141	+%0A    @Override	=950
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
}
(DIFF_FROM_PREVIOUS_FILE)
=146	+mp.shapes.AScalableRectangleInterface;%0Aimport mp.shapes.Gorge;%0Aimport 	=256	+();%0A    public void passed();%0A    public void failed();%0A    public void approach(final Avatar avatar);%0A    public void say(final String s);%0A    public AScalableRectangleInterface getKnightArea();%0A    public AScalableRectangleInterface getGuardArea();%0A    public Gorge getGorge();%0A    public boolean getOccupied();%0A    public boolean getKnightTurn	=5
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fn = "images/Guard.jpg";
    private int x, y;
    public GuardHead() { 	
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) { 
    	x = n; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=422	+    @Override	=408
//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangleInterface.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public interface AScalableRectangleInterface extends BoundedShape{
	public void scale(int percentage);
	public void setHeight(int x);
	public void setWidth(int x);
}

(DIFF_FROM_PREVIOUS_FILE)
=294	+%09public void setHeight(int x);%0A%09public void setWidth(int x);%0A	=2
//END OF FILE
//START OF FILE: mp/shapes/GetRect.java
package mp.shapes;

public interface GetRect extends Get{
	public AScalableRectangleInterface getRectangle();
}

//END OF FILE
//START OF FILE: mp/shapes/AScalableRectangle.java
package mp.shapes;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.Tags;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern("Rectangle Pattern")
public class AScalableRectangle implements AScalableRectangleInterface, Locatable{
	int x, y, width, height;
	int percentConversion = 100;
	public AScalableRectangle(int a, int b, int w, int h) {
		this.x = a;
		this.y = b;
		this.width = w;
		this.height = h;
	}
	@Override
	public int getX() {return x;}
	@Override
	public int getY() {return y;}
	@Override
	public int getWidth() {return width;}	
	@Override
	public int getHeight() { return height;}
	@Override
	public void setHeight(int x) {height = x;}
	@Override
	public void setWidth(int x) {width = x;}
	@Override
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int a) {
		x = a;
	}
	@Override
	public void setY(int b) {
		y = b;
	}
}
(DIFF_FROM_PREVIOUS_FILE)
=643	+%09@Override	=28	-6	+x	=12	-6	+x	=3	+%09@Override	=27	-6	+x	=11	-6	+x	=254
//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape, Moveable{
    void setRadius(double r);
    void setAngle(double angle);
    void rotate(int units);
    int getHeight();
}
(DIFF_FROM_PREVIOUS_FILE)
=407	+    int getHeight();%0A	=1
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fn = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    @Override
    public String getImageFileName() { 
    	return fn; 
    }
    @Override
    public void setImageFileName(String file) { 
    	fn = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int n) {
    	x = n; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int n) { 
    	y = n; 
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=427	+@Override%0A    	=395
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;
import mp.shapes.Get;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle, Get{
    private final RotateLine left, right;
    public VShape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int dx, int dy) {
        left.move(dx, dy);
        right.move(dx, dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=36	+Get;%0Aimport mp.shapes.	=361	+, Get	=415
//END OF FILE
//START OF FILE: mp/shapes/Get.java
package mp.shapes;

public interface Get {
 public RotateLine getLeftLine();
 public RotateLine getRightLine();
 
}

//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine, Moveable, PolarPointInterface{
    private Point point;
    private int a, b;
    private static final double UNIT = Math.PI / 32;
    public RotatingLine() {
        this.a = 0;
        this.b = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return a + point.getX();
    }
    @Override
    public void setX(final int x) {
        a = x;
    }
    @Override
    public int getY() {
        return b + point.getY();
    }
    @Override
    public void setY(final int y) {
        b = y;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }
    @Override
    public int getHeight() {
        return point.getY();
    }
    @Override
    public double getRadius() {
        return point.getRadius();
    }
    @Override
    public void setRadius(final double r) {
        point = new APolarPoint(r, point.getAngle());
    }
    @Override
    public double getAngle() {
        return point.getAngle();
    }
    @Override
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }
    @Override
    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }
    @Override
    public void move(final int dx, final int dy) {
        setX(a + dx);
        setY(b + dy);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=922	+    @Override	=1	-0	=150	+    @Override%0A	=188	+    @Override%0A	=112	+    @Override	=217
//END OF FILE

//SESSION END

//SESSION START
121,Wed Jul 09 15:52:16 EDT 2025,766
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X = 10;
    public static final int SOME_Y = 50;
    public static final int L_CONST = 8;
    public static final int R_CONST = 15;
    public static final int GAL_CONST = 22;
    public static final int GUARD_CONST = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X = 750;
    private static int gorgey = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X, SOME_Y);
      lancelot.move(SOME_X*L_CONST, SOME_Y);
      robin.move(SOME_X*R_CONST, SOME_Y);
      galahad.move(SOME_X*GAL_CONST,SOME_Y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(GORGE_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		occupied = false;
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_X);
    	cur.getHead().setY(gorgey);
    	gorgey += diff;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X);
        	guard.getHead().setY(gorgey);
        	gorgey += diff;}
    		}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	occupied = true;
    	cur = avatar;
    }
    @Override
    public void say(final String s){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(s);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(s);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
    @Override
    public void scroll(final int dx, final int dy){
        cur.scroll(dx, dy);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=3775	+    @Override%0A    public void scroll(final int dx, final int dy)%7B%0A        cur.scroll(dx, dy);%0A    %7D%0A	=1
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
    void scroll(int dx, int dy);
}
(DIFF_FROM_PREVIOUS_FILE)
=822	+    void scroll(int dx, int dy);%0A	=1
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable{
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
    void scroll(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=449	+    void scroll(int dx, int dy);%0A	=2
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
    @Override
    public void scroll(final int dx, final int dy){
        arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
        arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        arms.getRightLine().setX(arms.getRightLine().getX() + dx);
        arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
        legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        legs.getRightLine().setX(legs.getRightLine().getX() + dx);
        legs.getRightLine().setY(legs.getRightLine().getY() + dy);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=1426	+    @Override%0A    public void scroll(final int dx, final int dy)%7B%0A        arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);%0A        arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);%0A        arms.getRightLine().setX(arms.getRightLine().getX() + dx);%0A        arms.getRightLine().setY(arms.getRightLine().getY() + dy);%0A        legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);%0A        legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);%0A        legs.getRightLine().setX(legs.getRightLine().getX() + dx);%0A        legs.getRightLine().setY(legs.getRightLine().getY() + dy);%0A    %7D%0A	=1
//END OF FILE

//SESSION END

//SESSION START
122,Wed Jul 09 15:58:27 EDT 2025,-225
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X = 10;
    public static final int SOME_Y = 50;
    public static final int L_CONST = 8;
    public static final int R_CONST = 15;
    public static final int GAL_CONST = 22;
    public static final int GUARD_CONST = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X = 750;
    private static int gorgey = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X, SOME_Y);
      lancelot.move(SOME_X*L_CONST, SOME_Y);
      robin.move(SOME_X*R_CONST, SOME_Y);
      galahad.move(SOME_X*GAL_CONST,SOME_Y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(GORGE_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		occupied = false;
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_X);
    	cur.getHead().setY(gorgey);
    	gorgey += diff;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X);
        	guard.getHead().setY(gorgey);
        	gorgey += diff;}
    		}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	occupied = true;
    	cur = avatar;
    }
    @Override
    public void say(final String s){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(s);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(s);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
    @Override
    public void scroll(final Avatar avatar, final int dx, final int dy){
        avatar.scroll(dx, dy);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=3818	+Avatar avatar, final 	=31	-2	+avata	=25
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
    void scroll(Avatar avatar, int dx, int dy);
}
(DIFF_FROM_PREVIOUS_FILE)
=838	+Avatar avatar, 	=18
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
    @Override
    public void scroll(final int dx, final int dy){
        arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
        arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        arms.getRightLine().setX(arms.getRightLine().getX() + dx);
        arms.getRightLine().setY(arms.getRightLine().getY() + dy);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=1760	-264	=3
//END OF FILE

//SESSION END

//SESSION START
123,Wed Jul 09 18:17:01 EDT 2025,1047
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class Gorge implements GetRect{
    int rightlinex = 950;
    int linetopy = 0;
    int lineheight = 1000;
    int c1 = 0;
    int c2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScalableRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(lineheight);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(linetopy);
        leftLine.move(c1, c2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(lineheight);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(rightlinex);
        rightLine.setY(linetopy);
        rightLine.move(c1, c2);
        
        rectangle = new AScalableRectangle(x, upper, rightlinex - x, lower);
    }
    @Override
    public RotateLine getLeftLine() {
    	return leftLine;
    }
    @Override
    public RotateLine getRightLine(){
    	return rightLine;
    }
    @Override
    public AScalableRectangleInterface getRectangle() {
    	return rectangle;
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=20	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport util.annotations.Tags;%0A%0A@StructurePattern(StructurePatternNames.BEAN_PATTERN)%0A	=1111
//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.POINT_PATTERN)
public class APolarPoint implements Point, Locatable, PolarPointInterface{
	double radius, angle;
	public APolarPoint(double theRadius, double theAngle) {
		radius = theRadius;
		angle = theAngle;
	}
	public APolarPoint(int theX, int theY) {
		radius = Math.sqrt(theX*theX + theY*theY);
		angle = Math.atan((double) theY/theX);
	}
	@Override
	public int getX() { return (int) (radius*Math.cos(angle)); }
	@Override
	public int getY() { return (int) (radius*Math.sin(angle)); }
	@Override
	public double getAngle() { return angle; } 
	@Override
	public double getRadius() { return radius;}
	@Override
	public void setX(int x) {
	}
	@Override
	public void setY(int y) {
	}	
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport util.annotations.Tags;%0A%0A@Tags(%7BComp301Tags.LOCATABLE%7D)%0A@StructurePattern(StructurePatternNames.POINT_PATTERN)%0A	=674
//END OF FILE
//START OF FILE: mp/shapes/PolarPointInterface.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.POINT_PATTERN)
public interface PolarPointInterface extends Locatable{
	public double getAngle();
	public double getRadius();
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport util.annotations.Tags;%0A%0A@Tags(%7BComp301Tags.LOCATABLE%7D)%0A@StructurePattern(StructurePatternNames.POINT_PATTERN)%0A	=113
//END OF FILE
//START OF FILE: mp/shapes/Get.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags({Comp301Tags.ANGLE})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Get {
 public RotateLine getLeftLine();
 public RotateLine getRightLine();
 
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport util.annotations.Tags;%0A%0A@Tags(%7BComp301Tags.ANGLE%7D)%0A@StructurePattern(StructurePatternNames.BEAN_PATTERN)%0A	=96
//END OF FILE
//START OF FILE: mp/shapes/GetRect.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

public interface GetRect extends Get{
	public AScalableRectangleInterface getRectangle();
}

(DIFF_FROM_PREVIOUS_FILE)
=20	+import tags301.Comp301Tags;%0Aimport util.annotations.StructurePattern;%0Aimport util.annotations.StructurePatternNames;%0Aimport util.annotations.Tags;%0A%0A	=92
//END OF FILE

//SESSION END

//SESSION START
124,Wed Jul 09 18:19:43 EDT 2025,552
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X = 10;
    public static final int SOME_Y = 50;
    public static final int L_CONST = 8;
    public static final int R_CONST = 15;
    public static final int GAL_CONST = 22;
    public static final int GUARD_CONST = 30;
    private Gorge gorge;
    private Avatar cur;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X = 500;
    private static final int KNIGHT_Y = 600; 
    private static final int GUARD_Y = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X = 750;
    private static int gorgey = 0;
    int diff = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X, SOME_Y);
      lancelot.move(SOME_X*L_CONST, SOME_Y);
      robin.move(SOME_X*R_CONST, SOME_Y);
      galahad.move(SOME_X*GAL_CONST,SOME_Y);
      guard.move(AREA_X,GUARD_Y);
      gorge = new Gorge(GORGE_X);
      knightArea = new AScalableRectangle(AREA_X,KNIGHT_Y,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X,GUARD_Y,AREA_WIDTH,AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_X, KNIGHT_Y);
    		occupied = false;
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_X);
    	cur.getHead().setY(gorgey);
    	gorgey += diff;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X);
        	guard.getHead().setY(gorgey);
        	gorgey += diff;}
    		}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X, KNIGHT_Y);}
    	occupied = true;
    	cur = avatar;
    }
    @Override
    public void say(final String s){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(s);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(s);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
    @Override
    public void scroll(final String limb, final int dx, final int dy){
        cur.scroll(limb, dx, dy);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=3818	-3	+S	=1	-1	=1	+ing	=1	-6	+limb	=39	-5	+cu	=9	+limb, 	=16
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
    void scroll(String limb, int dx, int dy);
}
(DIFF_FROM_PREVIOUS_FILE)
=838	-3	+S	=1	-1	=1	+ing	=1	-6	+limb	=20
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable{
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
    void scroll(String limb, int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=465	+String limb, 	=19
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
    @Override
    public void scroll(final String limb, final int dx, final int dy){
        if (limb.equalsIgnoreCase("left arm")){
            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right arm")){
            arms.getRightLine().setX(arms.getRightLine().getX() + dx);
            arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("left leg")){
            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right leg")){
            legs.getRightLine().setX(legs.getRightLine().getX() + dx);
            legs.getRightLine().setY(legs.getRightLine().getY() + dy);
        }
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=1469	+String limb, final 	=31	+if (limb.equalsIgnoreCase(%22left arm%22))%7B%0A            	=57	+    	=65	+        %7D else if (limb.equalsIgnoreCase(%22right arm%22))%7B%0A    	=75	+    	=58	+%0A        %7D else if (limb.equalsIgnoreCase(%22left leg%22))%7B%0A            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);%0A            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);%0A        %7D else if (limb.equalsIgnoreCase(%22right leg%22))%7B%0A            legs.getRightLine().setX(legs.getRightLine().getX() + dx);%0A            legs.getRightLine().setY(legs.getRightLine().getY() + dy);%0A        %7D	=8
//END OF FILE

//SESSION END

//SESSION START
125,Wed Jul 09 18:33:24 EDT 2025,875
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape{
    String fileName = "images/lancelot.jpg";
    int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }
    @Override
    public void setImageFileName(final String fileName) {
        this.fileName = fileName;
    }
    @Override
    public int getX() {
        return x;
    }
    @Override
    public void setX(final int value) {
        x = value;
    }
    @Override
    public int getY() {
        return y;
    }
    @Override
    public void setY(final int value) {
        y = value;
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import mp.shapes.Locatable;%0Aimport tags301.Comp301Tags;%0Aimport util.annotations.Tags;	=90	-86	=18	+IM	=1	-1	+GE_P	=2	-1	+TE	=1	+N	=110	-8	=8	-1	+ileName	=29	-8	=97	+%0A  	=1	-1	=4	-1	+ 	=8	-1	+ileName	=1	-1	=54	+final 	=11	+Name	=3	-1	=5	-1	+    	=6	-1	+ileName	=7	+Name	=1	-1	=44	-1	=5	-1	+    	=9	-1	=42	+final 	=4	+v	=1	+lue	=8	-6	+    	=4	+v	=1	+lue	=1	-1	=49	-1	+    	=9	-1	=42	+final 	=4	-1	+value	=3	-1	=5	-6	+    	=4	-1	+value	=1	-1	=9
//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape{
    String fileName = "images/arthur.jpg";
    int x, y;
    public ArthurHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }
    @Override
    public void setImageFileName(final String fileName) {
        this.fileName = fileName;
    }
    @Override
    public int getX() {
        return x;
    }
    @Override
    public void setX(final int value) {
        x = value;
    }
    @Override
    public int getY() {
        return y;
    }
    @Override
    public void setY(final int value) {
        y = value;
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import mp.shapes.Locatable;%0Aimport tags301.Comp301Tags;	=120	-56	=18	+IM	=1	-1	+GE_P	=2	-1	+TE	=1	+N	=102	-1	=5	-8	=9	-1	+ileName	=28	-8	=94	-1	=5	-1	+    	=8	-1	+ileName	=1	-1	=54	+final 	=11	+Name	=3	+%0A 	=1	-1	+  	=4	-1	+this.	=1	-1	+ileName	=6	+eNam	=2	-1	=44	+%0A  	=1	-1	+ 	=4	-1	=9	-1	=42	+final 	=4	-1	+value	=4	+  	=4	-1	+  	=4	-1	+value	=1	-1	=49	-1	+    	=9	-1	=42	+final 	=4	-1	+value	=3	+%0A	=1	-1	=4	-1	+   	=4	-1	+value	=1	-1	=9
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape{
    String fileName = "images/galahad.jpg";
    int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }
    @Override
    public void setImageFileName(final String fileName) {
        this.fileName = fileName;
    }
    @Override
    public int getX() {
        return x;
    }
    @Override
    public void setX(final int value) {
        x = value;
    }
    @Override
    public int getY() {
        return y;
    }
    @Override
    public void setY(final int value) {
        y = value;
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=26	+mp.shapes.Locatable;%0Aimport tags301.Comp301Tags;%0Aimport 	=112	-56	=18	+IM	=1	-1	+GE_P	=2	-1	+TE	=1	+N	=109	-8	=8	-1	+ileName	=28	-8	=96	-1	=5	-1	+    	=8	-1	+ileName	=1	-1	=71	+Name	=3	+%0A 	=1	-1	+  	=4	-1	+this.	=1	-1	+ileName	=7	+Name	=1	-1	=44	+%0A	=1	-1	+   	=4	-1	=9	-1	=52	-1	+value	=3	+%0A  	=1	-1	=4	-1	+ 	=4	-1	+value	=1	-1	=44	+%0A  	=1	-1	+ 	=4	-1	=9	-1	=52	-1	+value	=3	+%0A  	=1	-1	=4	-1	+ 	=4	-1	+value	=1	-1	=9
//END OF FILE
//START OF FILE: main/Assignment2.java
package main;

import bus.uigen.OEFrame;
import bus.uigen.ObjectEditor;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import mp.bridge.BridgeScene;

public class Assignment2 {
    public static final int SOME_RADIUS = 300;
    public static final double SOME_ANGLE = Math.PI / 4;
    public static final int START_X = 30;
    public static final int START_Y = 30;
    public static final int DELTA = 1;
    public static final int COUNT = 100;
    public static final long SLEEP_MS = 50;

    public static void animateLine() throws InterruptedException {
        final RotateLine line = new RotatingLine();
        line.setRadius(SOME_RADIUS);
        line.setAngle(SOME_ANGLE);
        line.setX(START_X);
        line.setY(START_Y);

        final OEFrame frame = ObjectEditor.edit(line);
        for (int i = 0; i < COUNT; i++) {
            line.move(DELTA, DELTA);
            frame.refresh();
            Thread.sleep(SLEEP_MS);
        }
    }
    public static void main(final String[] args) throws InterruptedException {
    	final BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        ObjectEditor.edit(scene);
        animateLine();
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=226	+IUS	=8	-0	=170	+ELTA	=242	+IUS	=215	+ELTA	=3	+ELTA	=112	+final 	=123	-0	=31	+);%0A        animateLine(	=11
//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangle;
import mp.shapes.AScalableRectangleInterface;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_X_COORDINATE = 10;
    public static final int SOME_Y_COORDINATE = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GALAHAD_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private Gorge gorge;
    private Avatar currentAvatar;
    private AScalableRectangleInterface knightArea;
    private AScalableRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_X_COORDINATE = 500;
    private static final int KNIGHT_Y_COORDINATE = 600; 
    private static final int GUARD_Y_COORDINATE = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_X_COORDINATE = 750;
    private static int gorgeYCoordinate = 0;
    int difference = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_X_COORDINATE, SOME_Y_COORDINATE);
      lancelot.move(SOME_X_COORDINATE*LANCELOT_CONSTANT, SOME_Y_COORDINATE);
      robin.move(SOME_X_COORDINATE*ROBIN_CONSTANT, SOME_Y_COORDINATE);
      galahad.move(SOME_X_COORDINATE*GALAHAD_CONSTANT,SOME_Y_COORDINATE);
      guard.move(AREA_X_COORDINATE,GUARD_Y_COORDINATE);
      gorge = new Gorge(GORGE_X_COORDINATE);
      knightArea = new AScalableRectangle(AREA_X_COORDINATE,KNIGHT_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScalableRectangle(AREA_X_COORDINATE,GUARD_Y_COORDINATE,AREA_WIDTH,AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(!knightTurn){
    		currentAvatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);
    		occupied = false;
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	currentAvatar.getHead().setX(GORGE_X_COORDINATE);
    	currentAvatar.getHead().setY(gorgeYCoordinate);
    	gorgeYCoordinate += difference;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_X_COORDINATE);
        	guard.getHead().setY(gorgeYCoordinate);
        	gorgeYCoordinate += difference;}
    		}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_X_COORDINATE, KNIGHT_Y_COORDINATE);}
    	occupied = true;
    	currentAvatar = avatar;
    }
    @Override
    public void say(final String speechText){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(speechText);knightTurn= !knightTurn;} 
    		else {currentAvatar.getStringShape().setText(speechText);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    public AScalableRectangleInterface getKnightArea() {
        return knightArea;
    }
    public AScalableRectangleInterface getGuardArea() {
        return guardArea;
    }
    public Gorge getGorge() {return gorge;}
    public boolean getOccupied() {return occupied;}
    public boolean getKnightTurn() {return knightTurn;}
    @Override
    public void scroll(final String limb, final int dx, final int dy){
        currentAvatar.scroll(limb, dx, dy);
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=738	+_COORDINATE	=41	+_COORDINATE	=36	+ANCELOT	=5	+TAN	=36	+OBIN	=5	+TAN	=39	+AHAD	=5	+TAN	=47	+ANT	=53	+rrentAvata	=181	+_COORDINATE	=45	+_COORDINATE	=45	+_COORDINATE	=177	+_COORDINATE	=36	-1	+YCoordinate	=18	+erence	=309	+_COORDINATE	=8	+_COORDINATE	=29	+_COORDINATE	=2	+ANCELOT	=6	+ANT	=8	+_COORDINATE	=26	+_COORDINATE	=2	+OBIN	=5	+TAN	=9	+_COORDINATE	=28	+_COORDINATE	=4	+AHAD	=5	+TAN	=8	+_COORDINATE	=26	+_COORDINATE	=8	+_COORDINATE	=34	+_COORDINATE	=51	+_COORDINATE	=9	+_COORDINATE	=73	+_COORDINATE	=8	+_COORDINATE	=103	+rentAvatar	=12	+_COORDINATE	=10	+_COORDINATE	=132	+rentAvatar	=23	+_COORDINATE	=11	+rentAvatar	=21	-1	+YCoordinate	=13	-1	+YCoordinate	=8	+erence	=70	+_COORDINATE	=38	-1	+YCoordinate	=17	-1	+YCoordinate	=8	+erence	=116	+_COORDINATE	=10	+_COORDINATE	=34	+rentAvatar	=65	+peechText	=76	+peechText	=44	+rentAvatar	=27	+peechText	=862	+rrentAvata	=31
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
}
(DIFF_FROM_PREVIOUS_FILE)
=353	+extends Scrollable 	=466	-46	=4
//END OF FILE
//START OF FILE: mp/bridge/Scrollable.java
package mp.bridge;

public interface Scrollable {
    void scroll(String limb, int dx, int dy);
}
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable, Scrollable{
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
}

(DIFF_FROM_PREVIOUS_FILE)
=314	+, Scrollable	=118	-46	=19
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape{
    String fileName = "images/guard.jpg";
    int x, y;
    public GuardHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }
    @Override
    public void setImageFileName(final String fileName) {
        this.fileName = fileName;
    }
    @Override
    public int getX() {
        return x;
    }
    @Override
    public void setX(final int value) {
        x = value;
    }
    @Override
    public int getY() {
        return y;
    }
    @Override
    public void setY(final int value) {
        y = value;
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import mp.shapes.Locatable;%0Aimport tags301.Comp301Tags;	=120	-56	=18	+IM	=1	-1	+GE_P	=2	-1	+TE	=1	+N	=101	-1	=6	-8	=8	-1	+ileName	=11	-1	+g	=15	-8	=34	-2	=59	-1	=5	-1	+    	=8	-1	+ileName	=1	-1	=71	+Name	=3	+%0A	=1	-1	+   	=4	-1	+this.	=1	-1	+ileName	=7	+Name	=1	-1	=44	+%0A 	=1	-1	+  	=4	-1	=9	-1	=42	+final 	=4	-1	+value	=3	-1	=5	-1	+    	=4	-1	+value	=1	-1	=44	-1	=5	-1	+    	=9	-1	=42	+final 	=4	-1	+value	=3	+%0A	=1	-1	=4	-1	+   	=4	-1	+value	=1	-1	=9
//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends Locatable{
    String getImageFileName();
    void setImageFileName(final String fileName);
}

(DIFF_FROM_PREVIOUS_FILE)
=386	+final 	=8	-1	+ileName	=5
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape{
    String fileName = "images/robin.jpg";
    int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }
    @Override
    public void setImageFileName(final String fileName) {
        this.fileName = fileName;
    }
    @Override
    public int getX() {
        return x;
    }
    @Override
    public void setX(final int value) {
        x = value;
    }
    @Override
    public int getY() {
        return y;
    }
    @Override
    public void setY(final int value) {
        y = value;
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+import mp.shapes.Locatable;%0Aimport tags301.Comp301Tags;	=25	-10	+T	=1	-5	+gs	=42	-5	=26	-10	+St	=1	+uc	=1	-8	+ur	=1	-5	+P	=2	-3	+t	=1	-6	=1	-3	+nN	=1	-8	=1	-7	+e	=21	+IM	=1	-1	+GE_P	=2	-1	+TE	=1	+N	=107	-8	=8	-1	+ileName	=26	-8	=94	-1	=5	-1	+    	=8	-1	+ileName	=1	-1	=54	+final 	=11	+Name	=3	+%0A   	=1	-1	=4	-1	+this.	=1	-1	+ileName	=7	+Name	=1	-1	=44	-1	=5	-1	+    	=9	-1	=42	+f	=2	-1	+al	=1	+i	=1	+t value	=3	-1	=5	-1	+    	=4	-1	+value	=1	-1	=44	-1	=5	-1	+    	=9	-1	=42	+final 	=4	-1	+value	=3	+%0A	=1	-1	+   	=4	-1	=4	-1	+value	=1	-1	=9
//END OF FILE

//SESSION END

//SESSION START
126,Wed Jul 09 18:35:16 EDT 2025,93
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.Angle;
import mp.bridge.VShape;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER)
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass {
static BridgeScene scene;	

@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new VShape();		
}
}
(DIFF_FROM_PREVIOUS_FILE)
=202	+import tags301.Comp301Tags;%0Aimport util.annotations.Tags;%0A%0A@Tags(Comp301Tags.FACTORY_CLASS)%0A	=31	+ 	=285
//END OF FILE

//SESSION END

//SESSION START
127,Wed Jul 09 18:45:17 EDT 2025,29
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import mp.bridge.Scrollable;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
}
(DIFF_FROM_PREVIOUS_FILE)
=237	+import mp.bridge.Scrollable;%0A	=605
//END OF FILE

//SESSION END

//SESSION START
128,Wed Jul 09 19:06:51 EDT 2025,17257
//START OF FILE: mp/shapes/Locatable.java
package mp.shapes;

import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import util.models.PropertyListenerRegisterer;

@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface Locatable extends PropertyListenerRegisterer {
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
    List getPropertyChangeListeners();
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	+java.	=5	-7	+L	=1	-2	=1	-5	+t	=119	+import util.annotations.Tags;%0Aimport util.models.PropertyListenerRegisterer;%0A%0A	=110	+extends PropertyListenerRegisterer 	=87	+);%0A    List getPropertyChangeListeners(	=5
//END OF FILE
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fileName = "images/lancelot.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public LancelotHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	-5	+j	=1	-1	+va.b	=1	+an	=2	-1	+Pr	=1	-2	+per	=1	+yCh	=1	-2	+ngeEv	=1	+nt	=9	-1	+j	=1	-1	+va.bean	=1	-3	=1	-1	+Pr	=1	-1	=1	-4	+ertyCh	=1	+n	=1	+eLi	=1	+tener	=9	+java.	=5	+Arr	=1	-2	+yList;%0Aimp	=1	+r	=1	+ j	=1	+va.u	=2	+l.List;%0Aimp	=1	-1	+rt tag	=1	+301	=1	+Comp301	=95	+import util.annotations.Tags;%0A%0A	=135	+ 	=5	+ private	=45	+ private	=9	+, width = 50, height = 50	=1	+%0A    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();%0A	=35	+%0A	=84	+%0A	=80	+String oldValue = this.fileName;%0A        	=25	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22imageFileName%22, oldValue, fileName);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.x;%0A        this.	=10	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.y;%0A        this.	=9	+;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getWidth() %7B%0A        return width;%0A    %7D%0A%0A    @Override%0A    public void setWidth(final int width) %7B%0A        int oldValue = this.width;%0A        this.width = width;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22width%22, oldValue, width);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getHeight() %7B%0A        return height;%0A    %7D%0A%0A    @Override%0A    public void setHeight(final int height) %7B%0A        int oldValue = this.height;%0A        this.height = height;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22height%22, oldValue, height);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener)	=10
//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.LOCATABLE)
@StructurePattern(StructurePatternNames.STRING_PATTERN)
public class SpeechBubble implements StringShape, Locatable {
    private String text = "Grail";
    private int a, b;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public SpeechBubble() {
    }
    @Override
    public String getText() {
        return text;
    }

    @Override
    public void setText(final String t) {
        String oldValue = this.text;
        this.text = t;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "text", oldValue, t);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return a;
    }

    @Override
    public void setX(final int x) {
        int oldValue = this.a;
        this.a = x;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, x);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return b;
    }

    @Override
    public void setY(final int y) {
        int oldValue = this.b;
        this.b = y;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, y);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=27	-1	+java.beans.PropertyChangeEven	=1	+;%0A	=1	-1	+mport java	=1	+be	=2	+s.PropertyCha	=1	+geListener;%0Aimp	=1	+r	=1	+ j	=1	+va.u	=2	+l.ArrayList;%0Aimp	=1	-1	+rt java.util.Li	=1	+t;%0Aimport mp	=1	+shapes.Locatable;%0Aimport tags301.Comp301	=102	-2	+util	=1	-2	=1	-5	+nn	=1	-1	+t	=2	-6	=1	-2	=1	-6	+n	=1	-3	=1	-7	=5	+%0A	=134	+, Locatable 	=64	+rivate final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();%0A%0A    p	=72	-1	=5	-1	+    	=12	-1	=7	+%0A	=38	+final 	=11	+%0A  	=1	+     String oldValue = this.text;	=5	-1	+    this.	=9	+%0A      	=1	+ PropertyChangeEvent event = new PropertyChangeEvent(this, %22text%22, oldValue, t);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=37	-1	=5	-1	+    	=16	+%0A	=49	+%0A	=1	+       int oldValue = this.a;	=1	+  	=4	-1	+  this.	=6	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, x);%0A	=1	+       for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=37	-1	=5	-1	+    	=9	-1	=7	+%0A	=49	+%0A	=1	+       int oldValue = this.b;	=5	-1	+    this.	=6	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, y);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A 	=1	+          listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener);	=9
//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GuardHead implements ImageShape {
    private String fileName = "images/guard.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GuardHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	-5	+j	=1	-1	+va.b	=1	+an	=2	-1	+Pr	=1	-2	+per	=1	+yCh	=1	-2	+ngeEv	=1	+nt	=9	-1	+j	=1	-1	+va.bean	=1	-3	=1	-1	+Pr	=1	-1	=1	-4	+ertyCh	=1	+n	=1	+eLi	=1	+tener	=9	+java.	=5	+Arr	=1	-2	+yList;%0Aimp	=1	+r	=1	+ j	=1	+va.u	=2	+l.List;%0Aimp	=1	-1	+rt tag	=1	+301	=1	+Comp301	=95	+import util.annotations.Tags;%0A%0A	=132	+ 	=5	+ private	=42	+ private	=9	+, width = 50, height = 50	=1	+%0A    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();%0A	=32	+%0A	=84	+%0A	=80	+String oldValue = this.fileName;%0A        	=25	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22imageFileName%22, oldValue, fileName);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.x;%0A        this.	=10	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.y;%0A        this.	=9	+;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getWidth() %7B%0A        return width;%0A    %7D%0A%0A    @Override%0A    public void setWidth(final int width) %7B%0A        int oldValue = this.width;%0A        this.width = width;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22width%22, oldValue, width);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getHeight() %7B%0A        return height;%0A    %7D%0A%0A    @Override%0A    public void setHeight(final int height) %7B%0A        int oldValue = this.height;%0A        this.height = height;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22height%22, oldValue, height);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener)	=10
//END OF FILE
//START OF FILE: main/ConsoleSceneView.java
package main;

import java.beans.PropertyChangeListener;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public interface ConsoleSceneView extends PropertyChangeListener {
}

//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	-5	+j	=1	-1	+va.b	=1	+an	=2	-1	+Pr	=1	-2	+per	=1	+yCh	=1	-2	+ngeEv	=1	+nt	=9	-1	+j	=1	-1	+va.bean	=1	-3	=1	-1	+Pr	=1	-1	=1	-4	+ertyCh	=1	+n	=1	+eLi	=1	+tener	=9	+java.	=5	+Arr	=1	-2	+yList;%0Aimp	=1	+r	=1	+ j	=1	+va.u	=2	+l.List;%0Aimp	=1	-1	+rt tag	=1	+301	=1	+Comp301	=95	+import util.annotations.Tags;%0A%0A	=133	+ 	=5	+ private	=43	+ private	=9	+, width = 50, height = 50	=1	+%0A    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();%0A	=117	+%0A	=80	+String oldValue = this.fileName;%0A        	=25	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22imageFileName%22, oldValue, fileName);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.x;%0A        this.	=10	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.y;%0A        this.	=9	+;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getWidth() %7B%0A        return width;%0A    %7D%0A%0A    @Override%0A    public void setWidth(final int width) %7B%0A        int oldValue = this.width;%0A        this.width = width;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22width%22, oldValue, width);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getHeight() %7B%0A        return height;%0A    %7D%0A%0A    @Override%0A    public void setHeight(final int height) %7B%0A        int oldValue = this.height;%0A        this.height = height;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22height%22, oldValue, height);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener)	=10
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fileName = "images/galahad.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GalahadHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	-5	+j	=1	-1	+va.b	=1	+an	=2	-1	+Pr	=1	-2	+per	=1	+yCh	=1	-2	+ngeEv	=1	+nt	=9	-1	+j	=1	-1	+va.bean	=1	-3	=1	-1	+Pr	=1	-1	=1	-4	+ertyCh	=1	+n	=1	+eLi	=1	+tener	=9	+java.	=5	+Arr	=1	-2	+yList;%0Aimp	=1	+r	=1	+ j	=1	+va.u	=2	+l.List;%0Aimp	=1	-1	+rt tag	=1	+301	=1	+Comp301	=95	+import util.annotations.Tags;%0A%0A	=134	+ 	=5	+ private	=44	+ private	=9	+, width = 50, height = 50	=1	+%0A    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E();%0A	=34	+%0A	=84	+%0A	=80	+String oldValue = this.fileName;%0A        	=25	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22imageFileName%22, oldValue, fileName);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.x;%0A        this.	=10	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.y;%0A        this.	=9	+;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getWidth() %7B%0A        return width;%0A    %7D%0A%0A    @Override%0A    public void setWidth(final int width) %7B%0A        int oldValue = this.width;%0A        this.width = width;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22width%22, oldValue, width);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getHeight() %7B%0A        return height;%0A    %7D%0A%0A    @Override%0A    public void setHeight(final int height) %7B%0A        int oldValue = this.height;%0A        this.height = height;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22height%22, oldValue, height);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener)	=10
//END OF FILE
//START OF FILE: mp/bridge/VShape.java
package mp.bridge;

import mp.shapes.Get;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle, Get {
    private final RotateLine left, right;

    public VShape() {
        left = new RotatingLine();
        right = new RotatingLine();
    }

    @Override
    public RotateLine getLeftLine() {
        return left;
    }

    @Override
    public RotateLine getRightLine() {
        return right;
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        left.move(deltaX, deltaY);
        right.move(deltaX, deltaY);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=22	-55	=67	-9	=2	-4	+g	=1	+301	=1	+Comp301	=102	+u	=1	+il.	=1	-1	+nnotation	=1	-3	=1	-7	=127	+ 	=44	+%0A	=28	+  	=4	-1	=23	+  	=40	+%0A	=50	-1	=1	-1	=5	-1	+    	=12	-1	=7	+%0A	=52	-1	=5	-1	+    	=13	-1	=7	+%0A	=35	+final 	=5	-1	+eltaX	=2	+final 	=5	-1	+eltaY	=23	-1	+eltaX	=3	-1	+eltaY	=23	-1	+eltaX	=3	-1	+eltaY	=11
//END OF FILE
//START OF FILE: mp/shapes/BoundedShape.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape extends Locatable {
    int getWidth();
    void setWidth(final int width);
    int getHeight();
    void setHeight(final int height);
}
(DIFF_FROM_PREVIOUS_FILE)
=19	-29	=118	+import util.annotations.Tags;%0A%0A	=134	+ 	=26	+void setWidth(final int width);%0A    	=14	+);%0A    void setHeight(final int height	=4
//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;

import mp.bridge.Angle;
import mp.bridge.BridgeScene;
import mp.bridge.VShape;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass {
    private static BridgeScene scene;

    public static BridgeScene bridgeSceneFactoryMethod() {
        if (scene == null) {
            scene = new BridgeSceneImpl();
        }
        return scene;
    }

    public static ConsoleSceneView consoleSceneViewFactoryMethod() {
        return ConsoleSceneViewImpl.getInstance();
    }

    public static Angle legsFactoryMethod() {
        return new VShape();
    }

    // Placeholder methods for other required factory methods
    public static Object inheritingBridgeScenePainterFactoryMethod() {
        return null; // TODO: Implement when class is available
    }

    public static Object observableBridgeScenePainterFactoryMethod() {
        return null; // TODO: Implement when class is available
    }

    public static Object delegatingBridgeSceneViewFactoryMethod() {
        return null; // TODO: Implement when class is available
    }

    public static Object bridgeSceneControllerFactoryMethod() {
        return null; // TODO: Implement when class is available
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=14	+%0A	=24	-25	=37	-10	=2	-13	+.b	=1	-4	=1	-35	+d	=1	-1	+e	=1	-5	=1	-12	=1	-13	+hape;	=127	+    private 	=25	-1	=2	-32	+    	=52	+ 	=5	+     	=18	+ 	=1	+%0A            	=30	+%0A        	=5	+     	=14	+    	=2	-2	+%0A    public st	=1	-1	+tic Con	=1	+oleSceneView consoleSceneViewFactoryMethod	=1	+) %7B%0A        return 	=2	+nsoleSceneViewI	=2	-5	+l.	=1	+etIn	=1	-6	+tance(	=1	+;	=1	+    %7D%0A%0A    	=39	+ 	=2	-1	+     	=23	-2	+%0A    %7D%0A%0A    // Placeholder methods for other required factory methods%0A    public static Object inheritingBridgeScenePainterFactoryMethod() %7B%0A        return null; // TODO: Implement when class is available%0A    %7D%0A%0A    public static Object observableBridgeScenePainterFactoryMethod() %7B%0A        return null; // TODO: Implement when class is available%0A    %7D%0A%0A    public static Object delegatingBridgeSceneViewFactoryMethod() %7B%0A        return null; // TODO: Implement when class is available%0A    %7D%0A%0A    public static Object bridgeSceneControllerFactoryMethod() %7B	=1	+        return null; // TODO: Implement when class is available%0A    	=3
//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import mp.shapes.Moveable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable, Scrollable {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
    void scale(double factor);
}

(DIFF_FROM_PREVIOUS_FILE)
=27	-4	+mp	=1	+sh	=1	-2	+pes.M	=1	-1	+ve	=1	-1	+ble;%0A	=1	+mp	=1	-1	+rt tag	=1	+301	=1	+Comp301	=102	-2	+util	=1	-2	=1	-5	+nn	=1	-2	+t	=1	-5	+t	=1	-2	=1	-6	+n	=1	-3	=1	-7	=6	+%0A	=132	+ 	=104	-0	=28	+);%0A    void scale(double factor	=5
//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
    @Override
    public void scroll(final String limb, final int dx, final int dy) {
        if (limb.equalsIgnoreCase("left arm")) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right arm")) {
            arms.getRightLine().setX(arms.getRightLine().getX() + dx);
            arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("left leg")) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right leg")) {
            legs.getRightLine().setX(legs.getRightLine().getX() + dx);
            legs.getRightLine().setY(legs.getRightLine().getY() + dy);
        }
    }

    public void scale(final double factor) {
        // Scale the head (if it has width/height properties)
        if (head instanceof ImageShape) {
            head.setWidth((int) (head.getWidth() * factor));
            head.setHeight((int) (head.getHeight() * factor));
        }
    }
}
(DIFF_FROM_PREVIOUS_FILE)
=1509	+ 	=48	+ 	=194	+ 	=197	+ 	=194	+ 	=73	-0	=68	+);%0A        %7D%0A    %7D%0A%0A    public void scale(final double factor) %7B%0A        // Scale the head (if it has width/height properties)%0A        if (head instanceof ImageShape) %7B%0A            head.setWidth((int) (head.getWidth() * factor));%0A            head.setHeight((int) (head.getHeight() * factor)	=20
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;

import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ROTATING_LINE)
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine {
    private Point point;
    private int xOffset, yOffset;
    private static final double UNIT = Math.PI / 32;

    public RotatingLine() {
        this.xOffset = 0;
        this.yOffset = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return xOffset + point.getX();
    }

    @Override
    public void setX(final int x) {
        xOffset = x;
    }

    @Override
    public int getY() {
        return yOffset + point.getY();
    }

    @Override
    public void setY(final int y) {
        yOffset = y;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }
    @Override
    public int getHeight() {
        return point.getY();
    }
    @Override
    public double getRadius() {
        return point.getRadius();
    }

    @Override
    public void setRadius(final double radius) {
        point = new APolarPoint(radius, point.getAngle());
    }

    @Override
    public double getAngle() {
        return point.getAngle();
    }

    @Override
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }

    @Override
    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        setX(xOffset + deltaX);
        setY(yOffset + deltaY);
    }

    // Additional getters/setters for ROTATING_LINE requirements
    public int getXProperty() {
        return getX();
    }

    public void setXProperty(final int x) {
        setX(x);
    }

    public int getYProperty() {
        return getY();
    }

    public void setYProperty(final int y) {
        setY(y);
    }

    public double getRadiusProperty() {
        return getRadius();
    }

    public void setRadiusProperty(final double radius) {
        setRadius(radius);
    }

    public double getAngleProperty() {
        return getAngle();
    }

    public void setAngleProperty(final double angle) {
        setAngle(angle);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	-1	=1	-3	=1	-9	+g	=1	+301	=1	+Comp301	=102	+u	=1	+il.	=1	-1	+nnotation	=1	-3	=1	-7	=5	+%0A	=7	-1	=12	-1	+R	=1	-1	+T	=2	-2	+ING_	=1	+IN	=1	-1	=103	-1	=1	-29	=43	-1	+xOffset	=2	-1	+yOffset	=55	+%0A	=41	-1	+xOffset	=19	-1	+yOffset	=109	-1	+xOffset	=23	+%0A	=58	-1	+xOffset	=12	+%0A	=53	-1	+yOffset	=23	+%0A	=58	-1	+yOffset	=253	+%0A	=54	+adius	=37	+adius	=27	+%0A	=84	+%0A	=126	+%0A	=113	+%0A	=46	-1	+eltaX	=13	-1	+eltaY	=17	+xOffset + delt	=1	+X);%0A        setY(yOffset	=4	+eltaY);%0A    %7D%0A%0A    // Additional getters/setters for ROTATING_LINE requirements%0A    public int getXProperty() %7B%0A        return getX();%0A    %7D%0A%0A    public void setXProperty(final int x) %7B%0A        setX(	=8	+%7D%0A%0A	=4	+public int getYProperty() %7B%0A        return getY();%0A    %7D%0A%0A    public void 	=4	+Property	=1	+final int y) %7B%0A        setY(y);%0A    %7D%0A%0A    pu	=1	+lic	=1	-1	+double getRadiusProperty() %7B%0A        return getRadius();%0A    %7D%0A%0A    public	=1	+voi	=1	+ setRadiusPropert	=1	+(final double radius) %7B%0A        setRadius(radius);%0A    %7D%0A%0A    public double getAngleProperty() %7B%0A        return getAngle();%0A    %7D%0A%0A    public void setAngleProperty(final double angle) %7B%0A        setAngle(angle	=11
//END OF FILE
//START OF FILE: main/ConsoleSceneViewImpl.java
package main;

import java.beans.PropertyChangeEvent;
import java.util.List;
import mp.bridge.Avatar;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;

@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public class ConsoleSceneViewImpl implements ConsoleSceneView {
    
    private static ConsoleSceneViewImpl instance;

    private ConsoleSceneViewImpl() {
        BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        for (Avatar knight : List.of(
                scene.getArthur(),
                scene.getGalahad(),
                scene.getLancelot(),
                scene.getRobin(),
                scene.getGuard())) {
            knight.getHead().addPropertyChangeListener(this);
            knight.getStringShape().addPropertyChangeListener(this);
            knight.getArms().getLeftLine().addPropertyChangeListener(this);
            knight.getArms().getRightLine().addPropertyChangeListener(this);
            knight.getLegs().getLeftLine().addPropertyChangeListener(this);
            knight.getLegs().getRightLine().addPropertyChangeListener(this);
        }
    }

    public static ConsoleSceneViewImpl getInstance() {
        if (instance == null) {
            instance = new ConsoleSceneViewImpl();
        }
        return instance;
    }

    @Override
    public void propertyChange(final PropertyChangeEvent evt) {
        System.out.println(evt);
    }
}

//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;

import mp.shapes.BoundedShape;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends BoundedShape {
    String getImageFileName();
    void setImageFileName(final String fileName);
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=17	-1	+B	=1	-1	+undedSh	=1	-4	+p	=25	-30	=95	+import util.annotations.Tags;%0A%0A	=124	-1	+B	=1	-3	+undedSh	=1	-2	+p	=1	+ 	=85
//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape {
    private String fileName = "images/robin.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    public RobinHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

(DIFF_FROM_PREVIOUS_FILE)
=19	+%0A	=7	-5	+j	=1	-1	+va.b	=1	+an	=2	-1	+Pr	=1	-2	+per	=1	+yCh	=1	-2	+ngeEv	=1	+nt	=9	-1	+j	=1	-1	+va.bean	=1	-3	=1	-1	+Pr	=1	-1	=1	-4	+ertyCh	=1	+n	=1	+eLi	=1	+tener	=9	+java.	=5	+Arr	=1	-2	+yList;%0Aimp	=1	+r	=1	+ j	=1	+va.u	=2	+l.List;%0Aimp	=1	-1	+rt tag	=1	+301	=1	+Comp301	=95	+import util.annotations.Tags;%0A%0A	=132	+ 	=5	+ private	=42	+ private	=9	+, width = 50, height = 50;%0A    private final List%3CPropertyChangeListener%3E propertyChangeListeners = new ArrayList%3C%3E()	=33	+%0A	=84	+%0A	=80	+String oldValue = this.fileName;%0A        	=25	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22imageFileName%22, oldValue, fileName);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.x;%0A        this.	=10	+%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22x%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D	=7	+%0A	=62	+%0A	=62	+int oldValue = this.y;%0A        this.	=9	+;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22y%22, oldValue, value);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getWidth() %7B%0A        return width;%0A    %7D%0A%0A    @Override%0A    public void setWidth(final int width) %7B%0A        int oldValue = this.width;%0A        this.width = width;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22width%22, oldValue, width);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public int getHeight() %7B%0A        return height;%0A    %7D%0A%0A    @Override%0A    public void setHeight(final int height) %7B%0A        int oldValue = this.height;%0A        this.height = height;%0A        PropertyChangeEvent event = new PropertyChangeEvent(this, %22height%22, oldValue, height);%0A        for (PropertyChangeListener listener : propertyChangeListeners) %7B%0A            listener.propertyChange(event);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public List%3CPropertyChangeListener%3E getPropertyChangeListeners() %7B%0A        return new ArrayList%3C%3E(propertyChangeListeners);%0A    %7D%0A%0A    @Override%0A    public void addPropertyChangeListener(final PropertyChangeListener listener) %7B%0A        if (listener != null && !propertyChangeListeners.contains(listener)) %7B%0A            propertyChangeListeners.add(listener);%0A        %7D%0A    %7D%0A%0A    @Override%0A    public void removePropertyChangeListener(final PropertyChangeListener listener) %7B%0A        propertyChangeListeners.remove(listener)	=10
//END OF FILE

//SESSION END

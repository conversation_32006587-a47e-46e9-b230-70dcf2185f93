package mp.bridge;

import mp.shapes.Get;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class VShape implements Angle, Get {
    private final RotateLine left, right;

    public VShape() {
        left = new RotatingLine();
        right = new RotatingLine();
    }

    @Override
    public RotateLine getLeftLine() {
        return left;
    }

    @Override
    public RotateLine getRightLine() {
        return right;
    }

    @Override
    public void move(final int deltaX, final int deltaY) {
        left.move(deltaX, deltaY);
        right.move(deltaX, deltaY);
    }
}

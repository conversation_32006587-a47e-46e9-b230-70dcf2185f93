	<module name="ClassDefined">
		<property name="severity" value="info"/>
		<property name="expectedTypes" value="
			@Comp301Tags.ANGLE,
			@Comp301Tags.AVATAR,
			@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.FACTORY_CLASS,
			@Comp301Tags.IMAGE_PATTERN,
			@Comp301Tags.LOCATABLE,
			@Comp301Tags.ROTATING_LINE,
			@main.Assignment2,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			animateLine:->.*,
			main:String[]->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			animateLine:->.*,
			main:String[]->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setRadius:double->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setAngle:double->void,
			bus.uigen.OEFrame!refresh:->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			java.lang.Math!atan:double->double,
			java.lang.Math!cos:double->double,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setRadius:double->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setAngle:double->void,
			bus.uigen.OEFrame!refresh:->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			java.lang.Math!atan:double->double,
			java.lang.Math!cos:double->double,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:@Comp301Tags.BOUNDED_SHAPE,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:@Comp301Tags.BOUNDED_SHAPE,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:@Comp301Tags.BOUNDED_SHAPE,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:@Comp301Tags.BOUNDED_SHAPE,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			scroll:String;int;int->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			scroll:String;int;int->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.AVATAR!scroll:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			java.lang.Math!cos:double->double,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.LOCATABLE!setText:*->.*,
			@Comp301Tags.AVATAR!setText:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.AVATAR!scroll:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			java.lang.Math!cos:double->double,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.LOCATABLE!setText:*->.*,
			@Comp301Tags.AVATAR!setText:*->.*,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedInterfaces" value="
			java.beans.PropertyChangeListener,
			java.util.EventListener,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedInterfaces" value="
			java.beans.PropertyChangeListener,
			java.util.EventListener,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value="
			Instance:@Comp301Tags.CONSOLE_SCENE_VIEW,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value="
			Instance:@Comp301Tags.CONSOLE_SCENE_VIEW,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedSignatures" value="
			propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedSignatures" value="
			propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			java.util.List!of:Object;Object;Object;Object;Object->List,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			java.util.List!add:Object->boolean,
			@Comp301Tags.IMAGE_PATTERN!addPropertyChangeListener:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BOUNDED_SHAPE!setRadius:double->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setAngle:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			java.lang.Math!atan:double->double,
			@Comp301Tags.LOCATABLE!addPropertyChangeListener:*->.*,
			java.io.PrintStream!println:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			java.lang.Math!cos:double->double,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			java.util.List!of:Object;Object;Object;Object;Object->List,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			java.util.List!add:Object->boolean,
			@Comp301Tags.IMAGE_PATTERN!addPropertyChangeListener:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BOUNDED_SHAPE!setRadius:double->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setAngle:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			java.lang.Math!atan:double->double,
			@Comp301Tags.LOCATABLE!addPropertyChangeListener:*->.*,
			java.io.PrintStream!println:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			java.lang.Math!cos:double->double,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			bridgeSceneControllerFactoryMethod:->Object,
			observableBridgeScenePainterFactoryMethod:->Object,
			delegatingBridgeSceneViewFactoryMethod:->Object,
			inheritingBridgeScenePainterFactoryMethod:->Object,
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			bridgeSceneControllerFactoryMethod:->Object,
			observableBridgeScenePainterFactoryMethod:->Object,
			delegatingBridgeSceneViewFactoryMethod:->Object,
			inheritingBridgeScenePainterFactoryMethod:->Object,
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			java.util.List!of:Object;Object;Object;Object;Object->List,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			java.util.List!add:Object->boolean,
			@Comp301Tags.IMAGE_PATTERN!addPropertyChangeListener:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BOUNDED_SHAPE!setRadius:double->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setAngle:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.LOCATABLE!addPropertyChangeListener:*->.*,
			@Comp301Tags.CONSOLE_SCENE_VIEW!getInstance:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			java.lang.Math!cos:double->double,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			java.util.List!of:Object;Object;Object;Object;Object->List,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			java.util.List!add:Object->boolean,
			@Comp301Tags.IMAGE_PATTERN!addPropertyChangeListener:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BOUNDED_SHAPE!setRadius:double->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setAngle:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.LOCATABLE!addPropertyChangeListener:*->.*,
			@Comp301Tags.CONSOLE_SCENE_VIEW!getInstance:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			java.lang.Math!cos:double->double,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:@Comp301Tags.AVATAR,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:@Comp301Tags.AVATAR,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			scale:double->void,
			scroll:String;int;int->void,
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			scale:double->void,
			scroll:String;int;int->void,
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.ROTATING_LINE!getWidth:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getHeight:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setHeight:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.IMAGE_PATTERN!setWidth:*->.*,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setWidth:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setHeight:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getWidth:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getWidth:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.IMAGE_PATTERN!getX:*->.*,
			@Comp301Tags.ROTATING_LINE!getWidth:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ROTATING_LINE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getHeight:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setHeight:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.IMAGE_PATTERN!setWidth:*->.*,
			@Comp301Tags.ROTATING_LINE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setWidth:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setHeight:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.ROTATING_LINE!setY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.IMAGE_PATTERN!setY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getY:*->.*,
			@Comp301Tags.ROTATING_LINE!getY:*->.*,
			@Comp301Tags.IMAGE_PATTERN!getWidth:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getWidth:*->.*,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.IMAGE_PATTERN"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Text:String,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			PropertyChangeListeners:java.util.List,
			Text:String,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Text:String,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Text:String,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			removePropertyChangeListener:java.beans.PropertyChangeListener->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!contains:Object->boolean,
			java.util.List!remove:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:@Comp301Tags.BOUNDED_SHAPE,
			LeftLine:@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:@Comp301Tags.BOUNDED_SHAPE,
			LeftLine:@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.ROTATING_LINE!move:*->.*,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			java.lang.Math!sin:double->double,
			java.lang.Math!cos:double->double,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			java.lang.Math!sin:double->double,
			java.lang.Math!cos:double->double,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			XProperty:int,
			Angle:double,
			X:int,
			Y:int,
			AngleProperty:double,
			Height:int,
			YProperty:int,
			Width:int,
			RadiusProperty:double,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			XProperty:int,
			Angle:double,
			X:int,
			Y:int,
			AngleProperty:double,
			Height:int,
			YProperty:int,
			Width:int,
			RadiusProperty:double,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			XProperty:int,
			Angle:double,
			X:int,
			Y:int,
			AngleProperty:double,
			YProperty:int,
			RadiusProperty:double,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			XProperty:int,
			Angle:double,
			X:int,
			Y:int,
			AngleProperty:double,
			YProperty:int,
			RadiusProperty:double,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
			rotate:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
			rotate:int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedCalls" value="
			java.lang.Math!atan:double->double,
			java.lang.Math!sqrt:double->double,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedCalls" value="
			java.lang.Math!atan:double->double,
			java.lang.Math!sqrt:double->double,
		"/>
	</module>

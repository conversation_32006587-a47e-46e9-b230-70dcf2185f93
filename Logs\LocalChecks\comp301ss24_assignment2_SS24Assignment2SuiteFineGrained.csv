#,Time,%Passes,Change,Test,Pass,Partial,Fail,Untested,SessionNumber,SessionRunNumber,IsSuite,SuiteTests,PrerequisiteTests,ExtraCreditTests,TestScores,FailFromPreReq,
0,Tue Jul 08 19:17:59 EDT 2025,0,0,BridgeSceneSemantics, , ,BridgeSceneApproachMethodDefined+ BridgeSceneDynamics+ BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,0,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
1,Tue Jul 08 19:18:01 EDT 2025,0,0,A2Factory, , ,A2MainCallsBridgeSceneFactoryMethod+ AvatarCallsLegFactoryMethod+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ ConsoleSceneViewFactoryMethodDefined+ ConsoleSceneViewSingletonFromFactory+ LegsFactoryMethodDefined+ TaggedFactory+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable ,0,1,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(0.0/2.0) , ,
2,Tue Jul 08 19:18:02 EDT 2025,0,0,A2ConsoleSceneView, , ,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView+ ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene+ ConsoleSceneViewPrintsPropertyChangeEvent+ ConsoleSceneViewRegistersWithLocatables+ ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedConsoleSceneView+ TaggedFactory TaggedLocatable+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape ,0,2,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
3,Tue Jul 08 19:18:05 EDT 2025,0,0,A2Observables, , ,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent+ BoundedShapeInstantiatesPropertyChangeEvent+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent+ LocatableInstantiatesPropertyChangeEvent+ LocatablePropertyChangeListenersProperty+ Locatable_IS_A_PropertyListenerRegisterer+ TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,0,3,true,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer , ,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,BoundedShapeAnnouncesPropertyChangeEvent-(0.0/0.0) BoundedShapeInstantiatesPropertyChangeEvent-(0.0/0.0) LocatableAnnouncesPropertyChangeEvent-(0.0/0.0) LocatableInstantiatesPropertyChangeEvent-(0.0/0.0) LocatablePropertyChangeListenersProperty-(0.0/0.0) Locatable_IS_A_PropertyListenerRegisterer-(0.0/0.0) , ,
4,Tue Jul 08 19:18:06 EDT 2025,0,0,BridgeSceneScroll, , ,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase+ BridgeSceneArthurScrollRightLegTestCase+ BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase+ BridgeSceneLancelotScrollLeftArmTestCase+ BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase+ BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined+ BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,0,4,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
5,Tue Jul 08 19:18:07 EDT 2025,0,0,A2Style, , ,A2CommonPropertiesAreInherited+ A2CommonSignaturesAreInherited+ A2Encapsulation+ A2InterfaceAsType+ A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames+ A2NamedConstants+ A2NamingConventions+ A2NoHiddenFields+ A2NoStarImports+ A2NonPublicAccessModifiersMatched+ A2PackageDeclarations+ A2PublicMethodsOverride+ A2SimplifyBooleanExpressions+ A2SimplifyBooleanReturns+ AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,0,5,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
6,Tue Jul 08 20:25:34 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
7,Tue Jul 08 20:25:35 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
8,Tue Jul 08 20:25:37 EDT 2025,0,0,BridgeSceneSayMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,2,false, , , ,BridgeSceneSayMethodDefined-(0.0/2.0) , ,
9,Tue Jul 08 20:25:39 EDT 2025,0,0,BridgeScenePassedMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,3,false, , , ,BridgeScenePassedMethodDefined-(0.0/2.0) , ,
10,Tue Jul 08 20:25:40 EDT 2025,0,0,BridgeSceneFailedMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,4,false, , , ,BridgeSceneFailedMethodDefined-(0.0/2.0) , ,
11,Tue Jul 08 20:25:40 EDT 2025,0,0,BridgeSceneDynamics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,5,false, , , ,BridgeSceneDynamics-(0.0/50.0) , ,
12,Tue Jul 08 20:25:47 EDT 2025,0,0,BridgeSceneScroll, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,6,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
13,Tue Jul 08 20:25:48 EDT 2025,0,0,BridgeSceneScroll, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,7,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
14,Tue Jul 08 20:25:50 EDT 2025,0,0,A2Style, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,8,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
15,Tue Jul 08 20:25:51 EDT 2025,0,0,A2Observables, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,9,true,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,TaggedLocatable ,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,BoundedShapeAnnouncesPropertyChangeEvent-(0.0/0.0) BoundedShapeInstantiatesPropertyChangeEvent-(0.0/0.0) LocatableAnnouncesPropertyChangeEvent-(0.0/0.0) LocatableInstantiatesPropertyChangeEvent-(0.0/0.0) LocatablePropertyChangeListenersProperty-(0.0/0.0) Locatable_IS_A_PropertyListenerRegisterer-(0.0/0.0) , ,
16,Tue Jul 08 20:25:53 EDT 2025,0,0,A2ConsoleSceneView, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,10,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
17,Tue Jul 08 20:25:54 EDT 2025,0,0,TaggedConsoleSceneView, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,11,false, , , ,TaggedConsoleSceneView-(0.0/0.0) , ,
18,Tue Jul 08 20:25:59 EDT 2025,0,0,A2Factory, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,12,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(0.0/2.0) , ,
19,Tue Jul 08 20:26:00 EDT 2025,0,0,BridgeSceneSingletonFromFactory, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,13,false, , , ,BridgeSceneSingletonFromFactory-(0.0/2.0) , ,
20,Tue Jul 08 20:26:02 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,1,14,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
21,Tue Jul 08 22:18:48 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,2,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
22,Tue Jul 08 22:18:51 EDT 2025,0,0,A2Factory, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,2,1,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(0.0/2.0) , ,
23,Tue Jul 08 22:18:52 EDT 2025,0,0,A2ConsoleSceneView, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,2,2,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
24,Tue Jul 08 22:18:53 EDT 2025,0,0,A2Observables, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,2,3,true,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer , ,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,BoundedShapeAnnouncesPropertyChangeEvent-(0.0/0.0) BoundedShapeInstantiatesPropertyChangeEvent-(0.0/0.0) LocatableAnnouncesPropertyChangeEvent-(0.0/0.0) LocatableInstantiatesPropertyChangeEvent-(0.0/0.0) LocatablePropertyChangeListenersProperty-(0.0/0.0) Locatable_IS_A_PropertyListenerRegisterer-(0.0/0.0) , ,
25,Tue Jul 08 22:18:54 EDT 2025,0,0,BridgeSceneScroll, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,2,4,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
26,Tue Jul 08 22:18:56 EDT 2025,0,0,A2Style, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,2,5,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
27,Tue Jul 08 22:19:03 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,3,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
28,Tue Jul 08 22:20:15 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,4,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
29,Tue Jul 08 22:21:16 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,5,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
30,Tue Jul 08 22:22:22 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,6,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
31,Tue Jul 08 22:25:47 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,7,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
32,Tue Jul 08 22:25:56 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,7,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
33,Tue Jul 08 22:29:09 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,8,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
34,Tue Jul 08 22:30:08 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,9,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
35,Tue Jul 08 22:30:09 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,9,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
36,Tue Jul 08 22:32:17 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,10,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
37,Tue Jul 08 22:32:20 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,10,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
38,Tue Jul 08 22:33:06 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,11,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
39,Tue Jul 08 22:33:07 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,11,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
40,Tue Jul 08 22:33:08 EDT 2025,0,0,BridgeSceneApproachMethodDefined, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,11,2,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
41,Tue Jul 08 22:33:23 EDT 2025,0,0,BridgeSceneSemantics, , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,12,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
42,Tue Jul 08 22:34:58 EDT 2025,3,3,BridgeSceneSemantics,BridgeSceneApproachMethodDefined+ BridgeSceneSayMethodDefined+ , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,13,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
43,Tue Jul 08 23:04:20 EDT 2025,7,4,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,14,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
44,Tue Jul 08 23:06:59 EDT 2025,7,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,15,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
45,Tue Jul 08 23:08:51 EDT 2025,7,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,16,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
46,Tue Jul 08 23:09:41 EDT 2025,7,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,BridgeSceneDynamics+ ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,17,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(42.5/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
47,Tue Jul 08 23:10:43 EDT 2025,9,2,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneDynamics+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,18,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
48,Tue Jul 08 23:11:20 EDT 2025,9,0,BridgeSceneSemantics,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,19,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
49,Tue Jul 08 23:11:27 EDT 2025,20,11,A2Style,A2Encapsulation+ A2NoStarImports+ A2NonPublicAccessModifiersMatched+ A2PackageDeclarations+ A2SimplifyBooleanExpressions+ A2SimplifyBooleanReturns+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited+ A2InterfaceAsType+ A2MnemonicNames+ A2NamedConstants+ A2PublicMethodsOverride+ ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,19,1,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(3.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.5/5.0) A2MnemonicNames-(7.1/10.0) A2NamedConstants-(8.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(3.8/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
50,Tue Jul 08 23:11:40 EDT 2025,20,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,20,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(0.0/2.0) , ,
51,Tue Jul 08 23:11:45 EDT 2025,20,0,A2ConsoleSceneView,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,20,1,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
52,Tue Jul 08 23:33:23 EDT 2025,20,0,BridgeSceneSemantics,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,21,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
53,Wed Jul 09 00:02:11 EDT 2025,20,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,22,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(0.0/2.0) , ,
54,Wed Jul 09 00:17:17 EDT 2025,27,7,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ LegsFactoryMethodDefined+ TaggedFactory+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,23,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,
55,Wed Jul 09 00:24:38 EDT 2025,27,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,24,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,
56,Wed Jul 09 00:26:04 EDT 2025,29,2,A2ConsoleSceneView,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory TaggedLocatable+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,25,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
57,Wed Jul 09 00:26:07 EDT 2025,29,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,25,1,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,
58,Wed Jul 09 00:26:12 EDT 2025,29,0,A2MainCallsBridgeSceneFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory LegsFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,25,2,false, , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) , ,
59,Wed Jul 09 00:56:15 EDT 2025,25,-4,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined- LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,26,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
60,Wed Jul 09 00:56:32 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,26,1,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
61,Wed Jul 09 00:57:24 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,27,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
62,Wed Jul 09 00:57:40 EDT 2025,25,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,27,1,false, , , ,AvatarCallsLegFactoryMethod-(0.0/2.0) , ,
63,Wed Jul 09 00:57:43 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,27,2,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
64,Wed Jul 09 00:58:35 EDT 2025,23,-2,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory- ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,28,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
65,Wed Jul 09 00:58:47 EDT 2025,23,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,28,1,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
66,Wed Jul 09 00:59:27 EDT 2025,25,2,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,29,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
67,Wed Jul 09 00:59:32 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,29,1,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
68,Wed Jul 09 00:59:55 EDT 2025,25,0,A2MainCallsBridgeSceneFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,29,2,false, , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) , ,
69,Wed Jul 09 00:59:59 EDT 2025,25,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,29,3,false, , , ,AvatarCallsLegFactoryMethod-(0.0/2.0) , ,
70,Wed Jul 09 01:00:01 EDT 2025,25,0,LegsFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,29,4,false, , , ,LegsFactoryMethodDefined-(0.0/2.0) , ,
71,Wed Jul 09 01:01:09 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory- ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,30,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
72,Wed Jul 09 01:01:25 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined- ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,31,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
73,Wed Jul 09 01:02:12 EDT 2025,25,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,32,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
74,Wed Jul 09 01:02:15 EDT 2025,25,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,32,1,false, , , ,AvatarCallsLegFactoryMethod-(0.0/2.0) , ,
75,Wed Jul 09 01:02:19 EDT 2025,25,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,32,2,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
76,Wed Jul 09 01:20:54 EDT 2025,27,2,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined+ BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,33,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
77,Wed Jul 09 01:21:01 EDT 2025,27,0,AvatarCallsLegFactoryMethod,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,33,1,false, , , ,AvatarCallsLegFactoryMethod-(0.0/2.0) , ,
78,Wed Jul 09 01:22:02 EDT 2025,27,0,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,34,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
79,Wed Jul 09 01:23:18 EDT 2025,30,3,A2Factory,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined+ ConsoleSceneViewSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,35,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
80,Wed Jul 09 01:23:48 EDT 2025,30,0,LegsFactoryMethodDefined,A2Encapsulation A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,35,1,false, , , ,LegsFactoryMethodDefined-(0.0/2.0) , ,
81,Wed Jul 09 01:25:26 EDT 2025,32,2,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod+ A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,36,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
82,Wed Jul 09 01:30:43 EDT 2025,30,-2,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory- LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,37,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
83,Wed Jul 09 01:31:38 EDT 2025,30,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,38,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
84,Wed Jul 09 01:33:13 EDT 2025,32,2,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory+ TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,39,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
85,Wed Jul 09 01:34:46 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,40,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
86,Wed Jul 09 01:36:04 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,41,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
87,Wed Jul 09 01:37:53 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,42,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
88,Wed Jul 09 01:39:55 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,43,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
89,Wed Jul 09 01:41:22 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,44,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
90,Wed Jul 09 01:41:25 EDT 2025,32,0,LegsFactoryMethodDefined,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,44,1,false, , , ,LegsFactoryMethodDefined-(0.0/2.0) , ,
91,Wed Jul 09 01:43:21 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,45,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
92,Wed Jul 09 01:44:49 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,46,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
93,Wed Jul 09 01:45:48 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,47,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
94,Wed Jul 09 01:48:45 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,48,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
95,Wed Jul 09 01:48:47 EDT 2025,32,0,LegsFactoryMethodDefined,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,48,1,false, , , ,LegsFactoryMethodDefined-(0.0/2.0) , ,
96,Wed Jul 09 01:52:06 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,49,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
97,Wed Jul 09 01:53:48 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,50,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
98,Wed Jul 09 01:54:08 EDT 2025,32,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,51,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
99,Wed Jul 09 01:54:56 EDT 2025,32,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory TaggedConsoleSceneView+ TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedLocatable- ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,51,1,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
100,Wed Jul 09 01:55:01 EDT 2025,34,2,A2Observables,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer+ TaggedConsoleSceneView TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,51,2,true,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer , ,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,BoundedShapeAnnouncesPropertyChangeEvent-(0.0/0.0) BoundedShapeInstantiatesPropertyChangeEvent-(0.0/0.0) LocatableAnnouncesPropertyChangeEvent-(0.0/0.0) LocatableInstantiatesPropertyChangeEvent-(0.0/0.0) LocatablePropertyChangeListenersProperty-(0.0/0.0) Locatable_IS_A_PropertyListenerRegisterer-(0.0/0.0) , ,
101,Wed Jul 09 01:55:03 EDT 2025,34,0,BridgeSceneScroll,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,51,3,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
102,Wed Jul 09 01:55:22 EDT 2025,34,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty TaggedLocatable ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,52,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
103,Wed Jul 09 01:58:31 EDT 2025,36,2,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,53,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
104,Wed Jul 09 02:06:24 EDT 2025,34,-2,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory- LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,54,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
105,Wed Jul 09 02:09:50 EDT 2025,34,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,55,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
106,Wed Jul 09 02:11:05 EDT 2025,41,7,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene+ ConsoleSceneViewPrintsPropertyChangeEvent+ ConsoleSceneViewRegistersWithLocatables+ ConsoleSceneViewSingletonFromFactory+ Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView+ ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,56,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(7.5/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
107,Wed Jul 09 02:12:04 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,57,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(7.5/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
108,Wed Jul 09 02:13:57 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,58,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(7.5/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
109,Wed Jul 09 02:15:21 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,59,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(7.5/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
110,Wed Jul 09 02:15:28 EDT 2025,41,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,59,1,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
111,Wed Jul 09 02:15:43 EDT 2025,47,6,A2Observables,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent+ LocatableInstantiatesPropertyChangeEvent+ LocatablePropertyChangeListenersProperty+ Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined ,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape ,59,2,true,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer , ,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,BoundedShapeAnnouncesPropertyChangeEvent-(0.0/0.0) BoundedShapeInstantiatesPropertyChangeEvent-(0.0/0.0) LocatableAnnouncesPropertyChangeEvent-(0.0/0.0) LocatableInstantiatesPropertyChangeEvent-(0.0/0.0) LocatablePropertyChangeListenersProperty-(0.0/0.0) Locatable_IS_A_PropertyListenerRegisterer-(0.0/0.0) , ,
112,Wed Jul 09 02:15:51 EDT 2025,54,7,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty+ LocatableXProperty+ LocatableYEditableProperty+ LocatableYProperty+ Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes+ A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty+ BoundedShapeHeightProperty+ BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty+ BoundedShapeWidthProperty+ BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape+ , ,59,3,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
113,Wed Jul 09 02:16:06 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,60,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
114,Wed Jul 09 02:17:22 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,61,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
115,Wed Jul 09 02:17:29 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,61,1,false, , , ,TaggedBoundedShape-(0.0/0.0) , ,
116,Wed Jul 09 02:19:07 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,62,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
117,Wed Jul 09 02:19:10 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,62,1,false, , , ,TaggedBoundedShape-(0.0/0.0) , ,
118,Wed Jul 09 02:19:43 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,63,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
119,Wed Jul 09 02:19:44 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,63,1,false, , , ,TaggedBoundedShape-(0.0/0.0) , ,
120,Wed Jul 09 02:20:10 EDT 2025,63,9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty+ BoundedShapeHeightProperty+ BoundedShapeWidthEditableProperty+ BoundedShapeWidthProperty+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape+ TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,64,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(2.0/2.0) BoundedShapeHeightProperty-(2.0/2.0) BoundedShapeWidthEditableProperty-(2.0/2.0) BoundedShapeWidthProperty-(2.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
121,Wed Jul 09 02:20:50 EDT 2025,63,0,A2ExpectedSuperTypes,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,64,1,false, , , ,A2ExpectedSuperTypes-(0.0/20.0) , ,
122,Wed Jul 09 02:21:51 EDT 2025,63,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,65,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(2.0/2.0) BoundedShapeHeightProperty-(2.0/2.0) BoundedShapeWidthEditableProperty-(2.0/2.0) BoundedShapeWidthProperty-(2.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
123,Wed Jul 09 02:22:39 EDT 2025,54,-9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty- BoundedShapeHeightProperty- BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty- BoundedShapeWidthProperty- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape- , ,66,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
124,Wed Jul 09 02:22:44 EDT 2025,54,0,TaggedBoundedShape,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,66,1,false, , , ,TaggedBoundedShape-(0.0/0.0) , ,
125,Wed Jul 09 02:23:00 EDT 2025,63,9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty+ BoundedShapeHeightProperty+ BoundedShapeWidthEditableProperty+ BoundedShapeWidthProperty+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape+ TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,67,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(2.0/2.0) BoundedShapeHeightProperty-(2.0/2.0) BoundedShapeWidthEditableProperty-(2.0/2.0) BoundedShapeWidthProperty-(2.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
126,Wed Jul 09 03:21:42 EDT 2025,63,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined , ,68,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(2.0/2.0) BoundedShapeHeightProperty-(2.0/2.0) BoundedShapeWidthEditableProperty-(2.0/2.0) BoundedShapeWidthProperty-(2.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
127,Wed Jul 09 03:23:21 EDT 2025,54,-9,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty- BoundedShapeHeightProperty- BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty- BoundedShapeWidthProperty- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape- , ,69,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
128,Wed Jul 09 03:31:37 EDT 2025,54,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,70,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(2.0/2.0) LocatableXProperty-(2.0/2.0) LocatableYEditableProperty-(2.0/2.0) LocatableYProperty-(2.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
129,Wed Jul 09 03:32:33 EDT 2025,45,-9,BridgeSceneSemantics,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneFailedMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined- BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined- BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,71,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
130,Wed Jul 09 03:32:53 EDT 2025,34,-11,A2Style,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited- A2Encapsulation- A2ExpectedSuperTypes A2InterfaceAsType- A2MnemonicNames- A2NamedConstants- A2NamingConventions A2NoHiddenFields A2NoStarImports- A2NonPublicAccessModifiersMatched- A2PackageDeclarations- A2PublicMethodsOverride- A2SimplifyBooleanExpressions- A2SimplifyBooleanReturns- AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,72,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
131,Wed Jul 09 03:34:41 EDT 2025,40,6,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined+ BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,73,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
132,Wed Jul 09 03:37:03 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,74,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
133,Wed Jul 09 03:37:07 EDT 2025,40,0,BridgeSceneDynamics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,74,1,false, , , ,BridgeSceneDynamics-(0.0/50.0) , ,
134,Wed Jul 09 03:37:11 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,74,2,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
135,Wed Jul 09 03:38:20 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,75,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
136,Wed Jul 09 03:38:21 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,75,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
137,Wed Jul 09 03:38:57 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,76,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
138,Wed Jul 09 03:38:59 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,76,1,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
139,Wed Jul 09 03:40:15 EDT 2025,40,0,A2Factory,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,77,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
140,Wed Jul 09 03:40:18 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,77,1,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
141,Wed Jul 09 03:40:23 EDT 2025,40,0,BridgeSceneApproachMethodDefined,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,77,2,false, , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) , ,
142,Wed Jul 09 03:41:24 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,78,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
143,Wed Jul 09 03:42:07 EDT 2025,40,0,A2Style,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,79,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
144,Wed Jul 09 03:43:50 EDT 2025,40,0,A2Style,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,80,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
145,Wed Jul 09 03:43:54 EDT 2025,40,0,A2PackageDeclarations,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,80,1,false, , , ,A2PackageDeclarations-(0.0/5.0) , ,
146,Wed Jul 09 03:44:12 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,81,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
147,Wed Jul 09 03:48:07 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,82,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
148,Wed Jul 09 03:49:38 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,83,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
149,Wed Jul 09 03:53:29 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,84,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
150,Wed Jul 09 03:56:51 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,85,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
151,Wed Jul 09 04:00:23 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,86,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
152,Wed Jul 09 04:04:52 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,87,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
153,Wed Jul 09 04:08:01 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,88,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
154,Wed Jul 09 04:08:13 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,89,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
155,Wed Jul 09 04:11:39 EDT 2025,41,1,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,90,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
156,Wed Jul 09 04:13:40 EDT 2025,41,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,91,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
157,Wed Jul 09 04:15:00 EDT 2025,41,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,92,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
158,Wed Jul 09 04:16:17 EDT 2025,43,2,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,93,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
159,Wed Jul 09 04:17:28 EDT 2025,40,-3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,94,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
160,Wed Jul 09 04:18:04 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,95,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
161,Wed Jul 09 04:18:04 EDT 2025,40,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,95,1,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
162,Wed Jul 09 04:18:22 EDT 2025,43,3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneDynamics+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,96,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
163,Wed Jul 09 04:18:37 EDT 2025,40,-3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,97,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
164,Wed Jul 09 04:18:53 EDT 2025,43,3,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneDynamics+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,98,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
165,Wed Jul 09 04:21:07 EDT 2025,43,0,BridgeSceneSemantics,A2MainCallsBridgeSceneFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,ConsoleSceneView ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,99,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
166,Wed Jul 09 04:21:17 EDT 2025,52,9,A2Style,A2Encapsulation+ A2MainCallsBridgeSceneFactoryMethod A2NonPublicAccessModifiersMatched+ A2PackageDeclarations+ A2SimplifyBooleanExpressions+ A2SimplifyBooleanReturns+ BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited+ A2InterfaceAsType+ A2MnemonicNames+ A2NamedConstants+ A2PublicMethodsOverride+ ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields A2NoStarImports AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,99,1,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(2.9/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.3/5.0) A2MnemonicNames-(7.3/10.0) A2NamedConstants-(8.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
167,Wed Jul 09 05:00:04 EDT 2025,52,0,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields A2NoStarImports AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,100,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(2.9/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.4/5.0) A2MnemonicNames-(7.0/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
168,Wed Jul 09 05:00:14 EDT 2025,52,0,A2MnemonicNames,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields A2NoStarImports AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,100,1,false, , , ,A2MnemonicNames-(7.0/10.0) , ,
169,Wed Jul 09 05:22:37 EDT 2025,54,2,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports+ A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,101,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.9/5.0) A2MnemonicNames-(6.8/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
170,Wed Jul 09 05:22:40 EDT 2025,54,0,A2NoHiddenFields,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,101,1,false, , , ,A2NoHiddenFields-(0.0/5.0) , ,
171,Wed Jul 09 05:24:52 EDT 2025,56,2,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields+ A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,102,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.9/5.0) A2MnemonicNames-(6.8/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
172,Wed Jul 09 05:25:31 EDT 2025,56,0,A2NamingConventions,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,102,1,false, , , ,A2NamingConventions-(0.0/5.0) , ,
173,Wed Jul 09 05:29:48 EDT 2025,56,0,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,103,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.9/5.0) A2MnemonicNames-(6.7/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
174,Wed Jul 09 05:29:51 EDT 2025,56,0,A2NamingConventions,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,103,1,false, , , ,A2NamingConventions-(0.0/5.0) , ,
175,Wed Jul 09 05:31:19 EDT 2025,58,2,A2Style,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions+ A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,104,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.9/5.0) A2MnemonicNames-(6.6/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
176,Wed Jul 09 05:31:23 EDT 2025,58,0,A2InterfaceAsType,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,104,1,false, , , ,A2InterfaceAsType-(4.9/5.0) , ,
177,Wed Jul 09 05:31:28 EDT 2025,58,0,A2NamedConstants,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,104,2,false, , , ,A2NamedConstants-(8.7/10.0) , ,
178,Wed Jul 09 05:32:19 EDT 2025,58,0,A2PublicMethodsOverride,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,104,3,false, , , ,A2PublicMethodsOverride-(1.9/5.0) , ,
179,Wed Jul 09 05:32:22 EDT 2025,58,0,A2MnemonicNames,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,104,4,false, , , ,A2MnemonicNames-(6.6/10.0) , ,
180,Wed Jul 09 05:33:36 EDT 2025,60,2,A2Style,A2Encapsulation A2InterfaceAsType+ A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,105,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.6/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
181,Wed Jul 09 05:33:40 EDT 2025,60,0,A2PublicMethodsOverride,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,105,1,false, , , ,A2PublicMethodsOverride-(1.9/5.0) , ,
182,Wed Jul 09 05:35:58 EDT 2025,58,-2,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions- AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,106,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
183,Wed Jul 09 05:36:01 EDT 2025,58,0,A2NamingConventions,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NamingConventions AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,106,1,false, , , ,A2NamingConventions-(0.0/5.0) , ,
184,Wed Jul 09 05:36:51 EDT 2025,60,2,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions+ A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,107,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.1/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
185,Wed Jul 09 05:40:36 EDT 2025,60,0,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,108,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.2/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(1.9/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
186,Wed Jul 09 05:46:29 EDT 2025,60,0,A2Style,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,109,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(6.2/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
187,Wed Jul 09 05:46:30 EDT 2025,60,0,A2PublicMethodsOverride,A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2CommonSignaturesAreInherited A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,109,1,false, , , ,A2PublicMethodsOverride-(2.3/5.0) , ,
188,Wed Jul 09 05:50:23 EDT 2025,61,1,A2Style,A2CommonSignaturesAreInherited+ A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,110,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.6/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.7/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
189,Wed Jul 09 05:50:32 EDT 2025,61,0,A2PublicMethodsOverride,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,110,1,false, , , ,A2PublicMethodsOverride-(2.7/5.0) , ,
190,Wed Jul 09 05:54:10 EDT 2025,61,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,111,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(8.7/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
191,Wed Jul 09 05:54:12 EDT 2025,61,0,A2PublicMethodsOverride,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2NamedConstants A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,111,1,false, , , ,A2PublicMethodsOverride-(2.3/5.0) , ,
192,Wed Jul 09 05:55:41 EDT 2025,63,2,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants+ A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,112,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
193,Wed Jul 09 05:59:17 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,113,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.4/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
194,Wed Jul 09 06:01:20 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,114,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
195,Wed Jul 09 06:03:36 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,115,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
196,Wed Jul 09 06:10:21 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,116,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
197,Wed Jul 09 06:10:25 EDT 2025,63,0,A2MnemonicNames,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,116,1,false, , , ,A2MnemonicNames-(6.5/10.0) , ,
198,Wed Jul 09 06:13:10 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,117,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
199,Wed Jul 09 06:13:10 EDT 2025,63,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,117,1,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(5.0/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(5.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(2.3/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
200,Wed Jul 09 06:13:16 EDT 2025,63,0,A2PublicMethodsOverride,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,117,2,false, , , ,A2PublicMethodsOverride-(2.3/5.0) , ,
201,Wed Jul 09 06:28:12 EDT 2025,60,-3,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2InterfaceAsType- A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields- AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,118,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
202,Wed Jul 09 15:33:39 EDT 2025,60,0,BridgeSceneSemantics,A2CommonSignaturesAreInherited A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,119,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
203,Wed Jul 09 15:33:43 EDT 2025,60,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory TaggedLocatable ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined LegsFactoryMethodDefined TaggedBoundedShape , ,119,1,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
204,Wed Jul 09 15:33:50 EDT 2025,49,-11,A2Factory,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedLocatable ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ConsoleSceneView ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod- A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory- ConsoleSceneViewFactoryMethodDefined- ConsoleSceneViewSingletonFromFactory- LegsFactoryMethodDefined TaggedBoundedShape TaggedFactory- , ,119,2,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(0.0/2.0) , ,
205,Wed Jul 09 15:33:51 EDT 2025,40,-9,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView- ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene- ConsoleSceneViewPrintsPropertyChangeEvent- ConsoleSceneViewRegistersWithLocatables- ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedBoundedShape TaggedConsoleSceneView- TaggedFactory TaggedLocatable- , ,119,3,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
206,Wed Jul 09 15:33:54 EDT 2025,32,-8,A2Inheritance,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty- LocatableXProperty- LocatableYEditableProperty- LocatableYProperty- TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,119,4,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(0.0/2.0) LocatableXProperty-(0.0/2.0) LocatableYEditableProperty-(0.0/2.0) LocatableYProperty-(0.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,
207,Wed Jul 09 15:34:00 EDT 2025,32,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,119,5,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.5/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
208,Wed Jul 09 15:41:20 EDT 2025,32,0,BridgeSceneArthurScrollLeftArmTestCase,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,120,0,false, , , ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) , ,
209,Wed Jul 09 15:52:16 EDT 2025,34,2,BridgeSceneScrollMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined+ LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,121,0,false, , , ,BridgeSceneScrollMethodDefined-(2.0/2.0) , ,
210,Wed Jul 09 15:53:20 EDT 2025,34,0,BridgeSceneLancelotScrollLeftArmTestCase,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,121,1,false, , , ,BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) , ,
211,Wed Jul 09 15:58:27 EDT 2025,34,0,TaggedFactory,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,0,false, , , ,TaggedFactory-(0.0/2.0) , ,
212,Wed Jul 09 15:58:37 EDT 2025,34,0,LegsFactoryMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,1,false, , , ,LegsFactoryMethodDefined-(0.0/2.0) , ,
213,Wed Jul 09 15:58:39 EDT 2025,34,0,A2MainCallsBridgeSceneFactoryMethod,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,2,false, ,BridgeSceneFactoryMethodDefined , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) , ,
214,Wed Jul 09 15:58:41 EDT 2025,34,0,AvatarCallsLegFactoryMethod,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,3,false, , , ,AvatarCallsLegFactoryMethod-(0.0/2.0) , ,
215,Wed Jul 09 15:58:48 EDT 2025,34,0,BridgeSceneSemantics,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,4,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,
216,Wed Jul 09 15:58:48 EDT 2025,34,0,BridgeSceneApproachMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,5,false, , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) , ,
217,Wed Jul 09 15:59:45 EDT 2025,34,0,LegsFactoryMethodDefined,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,122,6,false, , , ,LegsFactoryMethodDefined-(0.0/2.0) , ,
218,Wed Jul 09 18:17:01 EDT 2025,34,0,A2Style,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,123,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.3/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
219,Wed Jul 09 18:17:01 EDT 2025,32,-2,BridgeSceneScroll,A2CommonSignaturesAreInherited A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined- BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,123,1,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
220,Wed Jul 09 18:19:43 EDT 2025,30,-2,A2Style,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited- A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,124,0,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.4/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
221,Wed Jul 09 18:19:43 EDT 2025,30,0,A2Style,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,124,1,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.4/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
222,Wed Jul 09 18:19:48 EDT 2025,30,0,A2Style,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,124,2,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(7.0/7.0) A2Encapsulation-(5.0/5.0) A2InterfaceAsType-(4.8/5.0) A2MnemonicNames-(6.4/10.0) A2NamedConstants-(10.0/10.0) A2NamingConventions-(5.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(2.0/2.0) A2NonPublicAccessModifiersMatched-(5.0/5.0) A2PackageDeclarations-(5.0/5.0) A2PublicMethodsOverride-(4.6/5.0) A2SimplifyBooleanExpressions-(5.0/5.0) A2SimplifyBooleanReturns-(5.0/5.0) , ,
223,Wed Jul 09 18:19:50 EDT 2025,30,0,A2NoHiddenFields,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,124,3,false, , , ,A2NoHiddenFields-(0.0/5.0) , ,
224,Wed Jul 09 18:33:24 EDT 2025,30,0,TaggedFactory,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,125,0,false, , , ,TaggedFactory-(0.0/2.0) , ,
225,Wed Jul 09 18:33:31 EDT 2025,30,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,125,1,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
226,Wed Jul 09 18:33:33 EDT 2025,30,0,ConsoleSceneViewFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,125,2,false, , , ,ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) , ,
227,Wed Jul 09 18:33:35 EDT 2025,30,0,ConsoleSceneViewSingletonFromFactory,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedFactory TaggedLocatable , ,125,3,false, , , ,ConsoleSceneViewSingletonFromFactory-(0.0/2.0) , ,
228,Wed Jul 09 18:35:16 EDT 2025,32,2,A2Factory,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory+ ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,126,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(0.0/2.0) AvatarCallsLegFactoryMethod-(0.0/2.0) BridgeSceneFactoryMethodDefined-(0.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(0.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(0.0/2.0) TaggedFactory-(2.0/2.0) , ,
229,Wed Jul 09 18:35:25 EDT 2025,32,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,126,1,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
230,Wed Jul 09 18:35:29 EDT 2025,32,0,BridgeSceneFactoryMethodDefined,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneFactoryMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,126,2,false, , , ,BridgeSceneFactoryMethodDefined-(0.0/2.0) , ,
231,Wed Jul 09 18:45:17 EDT 2025,23,-9,BridgeSceneSemantics,A2Encapsulation A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2MainCallsBridgeSceneFactoryMethod A2NoHiddenFields AvatarCallsLegFactoryMethod BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined- BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics- BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined- BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined- BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined- BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,127,0,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(0.0/2.0) BridgeSceneDynamics-(0.0/50.0) BridgeSceneFailedMethodDefined-(0.0/2.0) BridgeScenePassedMethodDefined-(0.0/2.0) BridgeSceneSayMethodDefined-(0.0/2.0) , ,
232,Wed Jul 09 19:06:51 EDT 2025,32,9,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod+ A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod+ BridgeSceneFactoryMethodDefined+ ConsoleSceneViewFactoryMethodDefined+ LegsFactoryMethodDefined+ LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,128,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(2.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(0.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(0.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,
233,Wed Jul 09 19:06:58 EDT 2025,32,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,128,1,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,
234,Wed Jul 09 19:07:01 EDT 2025,27,-5,A2Observables,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent- LocatableInstantiatesPropertyChangeEvent- LocatablePropertyChangeListenersProperty- LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,128,2,true,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer , ,BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeInstantiatesPropertyChangeEvent LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty Locatable_IS_A_PropertyListenerRegisterer ,BoundedShapeAnnouncesPropertyChangeEvent-(0.0/0.0) BoundedShapeInstantiatesPropertyChangeEvent-(0.0/0.0) LocatableAnnouncesPropertyChangeEvent-(0.0/0.0) LocatableInstantiatesPropertyChangeEvent-(0.0/0.0) LocatablePropertyChangeListenersProperty-(0.0/0.0) Locatable_IS_A_PropertyListenerRegisterer-(0.0/0.0) , ,
235,Wed Jul 09 19:07:04 EDT 2025,27,0,BridgeSceneScroll,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2NamedConstants A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory ,A2CommonSignaturesAreInherited A2InterfaceAsType A2MnemonicNames A2PublicMethodsOverride ,A2CommonPropertiesAreInherited A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneApproachMethodDefined BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeScenePassedMethodDefined BridgeSceneRobinScrollLeftArmTestCase BridgeSceneSayMethodDefined BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,128,3,true,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined , ,BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ,BridgeSceneArthurScrollLeftArmTestCase-(0.0/2.0) BridgeSceneArthurScrollRightLegTestCase-(0.0/2.0) BridgeSceneGalahadScrollLeftArmTestCase-(0.0/2.0) BridgeSceneLancelotScrollLeftArmTestCase-(0.0/2.0) BridgeSceneRobinScrollLeftArmTestCase-(0.0/2.0) BridgeSceneScrollMethodDefined-(0.0/2.0) , ,
236,Wed Jul 09 19:07:06 EDT 2025,20,-7,A2Style,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined+ BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined+ BridgeScenePassedMethodDefined+ BridgeSceneSayMethodDefined+ ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited- A2Encapsulation- A2ExpectedSuperTypes A2InterfaceAsType- A2MnemonicNames- A2NamedConstants- A2NamingConventions- A2NoHiddenFields A2NoStarImports- A2NonPublicAccessModifiersMatched- A2PackageDeclarations- A2PublicMethodsOverride- A2SimplifyBooleanExpressions- A2SimplifyBooleanReturns- BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,128,4,true,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns ,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2NonPublicAccessModifiersMatched ,A2CommonPropertiesAreInherited-(0.0/7.0) A2CommonSignaturesAreInherited-(0.0/7.0) A2Encapsulation-(0.0/5.0) A2InterfaceAsType-(0.0/5.0) A2MnemonicNames-(0.0/10.0) A2NamedConstants-(0.0/10.0) A2NamingConventions-(0.0/5.0) A2NoHiddenFields-(0.0/5.0) A2NoStarImports-(0.0/2.0) A2NonPublicAccessModifiersMatched-(0.0/5.0) A2PackageDeclarations-(0.0/5.0) A2PublicMethodsOverride-(0.0/5.0) A2SimplifyBooleanExpressions-(0.0/5.0) A2SimplifyBooleanReturns-(0.0/5.0) , ,
237,Wed Jul 09 19:07:34 EDT 2025,20,0,A2PackageDeclarations,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined ConsoleSceneViewFactoryMethodDefined LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedFactory , ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2Encapsulation A2ExpectedSuperTypes A2InterfaceAsType A2MnemonicNames A2NamedConstants A2NamingConventions A2NoHiddenFields A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2PublicMethodsOverride A2SimplifyBooleanExpressions A2SimplifyBooleanReturns BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneDynamics BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables ConsoleSceneViewSingletonFromFactory LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedConsoleSceneView TaggedLocatable , ,128,5,false, , , ,A2PackageDeclarations-(0.0/5.0) , ,

package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fileName = "images/lancelot.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public LancelotHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String newFileName) {
        final String oldValue = this.fileName;
        this.fileName = newFileName;
        final PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, newFileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}

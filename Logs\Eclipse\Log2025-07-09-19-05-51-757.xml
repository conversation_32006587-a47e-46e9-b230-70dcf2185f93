<Events startTimestamp="1752102351757" logVersion="1.0.0.202503121800">
  <Command __id="2" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:05:58 EDT 2025" starttimestamp="1752102351757" timestamp="6723" />
  <Command __id="3" _type="ShellCommand" date="Wed Jul 09 19:06:08 EDT 2025" starttimestamp="1752102351757" timestamp="16331" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="11" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 19:06:18 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752102351757" timestamp="26630" type="Run" />
  <Command __id="12" _type="ConsoleOutput" date="Wed Jul 09 19:06:19 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="27719" type="ConsoleOutput">
    <outputString><![CDATA[Could not load class:.\src\main\ConsoleSceneView.java ClassNotFoundException main.ConsoleSceneView
]]></outputString>
    <diff><![CDATA[null]]></diff>
  </Command>
  <Command __id="13" _type="ConsoleOutput" date="Wed Jul 09 19:06:19 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="27788" type="ConsoleOutput">
    <outputString><![CDATA[Could not load class:.\src\mp\bridge\Scrollable.java ClassNotFoundException mp.bridge.Scrollable
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,"Could not load class:.\src\m"), Diff(DELETE,"ain\ConsoleSceneView"), Diff(INSERT,"p\bridge\Scrollable"), Diff(EQUAL,".java ClassNotFoundException m"), Diff(DELETE,"ain.ConsoleSceneView"), Diff(INSERT,"p.bridge.Scrollable"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="14" _type="ConsoleOutput" date="Wed Jul 09 19:06:19 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="27804" type="ConsoleOutput">
    <outputString><![CDATA[Could not load class:.\src\main\ConsoleSceneViewImpl.java ClassNotFoundException main.ConsoleSceneViewImpl
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,"Could not load class:.\src\m"), Diff(DELETE,"p\bridge\Scrollable"), Diff(INSERT,"ain\ConsoleSceneViewImpl"), Diff(EQUAL,".java ClassNotFoundException m"), Diff(DELETE,"p.bridge.Scrollable"), Diff(INSERT,"ain.ConsoleSceneViewImpl"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="15" _type="ShellCommand" date="Wed Jul 09 19:06:23 EDT 2025" starttimestamp="1752102351757" timestamp="31424" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="16" _type="ConsoleOutput" date="Wed Jul 09 19:06:45 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="53736" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Could not load class:.\src\main\ConsoleSceneViewImpl.java ClassNotFoundException main.ConsoleSceneViewImpl"), Diff(INSERT,">>Running suite A2Factory
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="17" _type="ConsoleOutput" date="Wed Jul 09 19:06:45 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="53798" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:06:45 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 19:06:45 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="18" _type="ConsoleOutput" date="Wed Jul 09 19:06:45 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="53820" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):42<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:06:45 EDT 2025<<
¶>>Running test TaggedFactory
¶"), Diff(INSERT,"TaggedFactory test execution time (ms):42"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="19" _type="ConsoleOutput" date="Wed Jul 09 19:06:45 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="53835" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedFactory test execution time (ms):42"), Diff(INSERT,"est Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="20" _type="ConsoleOutput" date="Wed Jul 09 19:06:45 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="53848" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:06:45 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"Wed Jul 09 19:06:45 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="21" _type="ConsoleOutput" date="Wed Jul 09 19:06:45 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="53969" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 19:06:45 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="22" _type="ConsoleOutput" date="Wed Jul 09 19:06:51 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="59430" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):5595<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):5595<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="23" _type="ConsoleOutput" date="Wed Jul 09 19:06:51 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="59441" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):5595"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="24" _type="ConsoleOutput" date="Wed Jul 09 19:06:51 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="59450" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:06:51 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 19:06:51 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="25" _type="ExceptionCommand" date="Wed Jul 09 19:06:51 EDT 2025" starttimestamp="1752102351757" timestamp="59460" type="Exception">
    <exceptionString><![CDATA[java.lang.NoClassDefFoundError: ConsoleSceneView
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
	at java.base/java.lang.Class.getMethods(Class.java:2019)
	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="26" _type="ConsoleOutput" date="Wed Jul 09 19:06:51 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="59476" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.NoClassDefFoundError: ConsoleSceneView
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
	at java.base/java.lang.Class.getMethods(Class.java:2019)
	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 19:06:51 EDT 2025<<
¶>>Running test BridgeSceneSingletonFromFactory
¶<<"), Diff(INSERT,"java.lang.NoClassDefFoundError: ConsoleSceneView
¶	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
¶	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
¶	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
¶	at java.base/java.lang.Class.getMethods(Class.java:2019)
¶	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="27" _type="ConsoleOutput" date="Wed Jul 09 19:06:51 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="59770" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):8<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
<<
>>Wed Jul 09 19:06:51 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"java.lang.NoClassDefFoundError: ConsoleSceneView
¶	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
¶	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
¶	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
¶	at java.base/java.lang.Class.getMethods(Class.java:2019)
¶	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)"), Diff(INSERT,">>BridgeSceneSingletonFromFactory test execution time (ms):8<<
¶>>Test Result:
¶BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
¶<<
¶>>Wed Jul 09 19:06:51 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="28" _type="ExceptionCommand" date="Wed Jul 09 19:06:51 EDT 2025" starttimestamp="1752102351757" timestamp="59822" type="Exception">
    <exceptionString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):39<<
java.lang.NoClassDefFoundError: ConsoleSceneView
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
	at java.base/java.lang.Class.getMethods(Class.java:2019)
	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment8.testcases.factory.ConsoleSceneViewFactoryMethodTest.doTest(ConsoleSceneViewFactoryMethodTest.java:43)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="29" _type="ConsoleOutput" date="Wed Jul 09 19:06:51 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="59896" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):39<<
java.lang.NoClassDefFoundError: ConsoleSceneView
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
	at java.base/java.lang.Class.getMethods(Class.java:2019)
	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment8.testcases.factory.ConsoleSceneViewFactoryMethodTest.doTest(ConsoleSceneViewFactoryMethodTest.java:43)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"BridgeSceneSingletonFromFactory"), Diff(INSERT,"ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"8"), Diff(INSERT,"39"), Diff(EQUAL,"<<
¶"), Diff(DELETE,">>Test Result:
¶BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,java.lang.NoClassDefFoundError ConsoleSceneView
¶<<
¶>>Wed Jul 09 19:06:51 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined
¶<<"), Diff(INSERT,"java.lang.NoClassDefFoundError: ConsoleSceneView
¶	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
¶	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
¶	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
¶	at java.base/java.lang.Class.getMethods(Class.java:2019)
¶	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment8.testcases.factory.ConsoleSceneViewFactoryMethodTest.doTest(ConsoleSceneViewFactoryMethodTest.java:43)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="30" _type="ConsoleOutput" date="Wed Jul 09 19:06:58 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="66551" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"ConsoleSceneViewFactoryMethodDefined test execution time (ms):39<<
¶java.lang.NoClassDefFoundError: ConsoleSceneView
¶	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
¶	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
¶	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3427)
¶	at java.base/java.lang.Class.getMethods(Class.java:2019)
¶	at grader.basics.project.BasicProjectIntrospection.findMethodsByTag(BasicProjectIntrospection.java:1790)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1747)
¶	at grader.basics.project.BasicProjectIntrospection.findUniqueMethodByTag(BasicProjectIntrospection.java:1734)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:314)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment8.testcases.factory.ConsoleSceneViewFactoryMethodTest.doTest(ConsoleSceneViewFactoryMethodTest.java:43)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)"), Diff(INSERT,"Running suite A2ConsoleSceneView
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="31" _type="ConsoleOutput" date="Wed Jul 09 19:06:58 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="66563" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:06:58 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):3<<
>>Steps traced since last test:

>>Test Result:
TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
<<
>>Wed Jul 09 19:06:58 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Wed Jul 09 19:06:58 EDT 2025<<
>>Running test TaggedLocatable
<<
>>Steps traced since last test:

>>TaggedLocatable test execution time (ms):1<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>ConsoleSceneViewGetsBridgeScene test execution time (ms):6<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 19:06:58 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Steps traced since last test:

>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 19:06:58 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView"), Diff(INSERT,"Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):3<<
¶>>Steps traced since last test:
¶
¶>>Test Result:
¶TaggedConsoleSceneView,0.0% complete,0.0,0.0,No class in project matching name/tag:ConsoleSceneView
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<
¶>>Steps traced since last test:
¶
¶>>TaggedLocatable test execution time (ms):1<<
¶>>Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<
¶>>ConsoleSceneViewGetsBridgeScene test execution time (ms):6<<
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>Steps traced since last test:
¶
¶>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="32" _type="ConsoleOutput" date="Wed Jul 09 19:06:58 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="66613" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:06:58 EDT 2025<<
>>Running test ConsoleSceneView
<<
>>ConsoleSceneView test execution time (ms):0<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test "), Diff(DELETE,"Tagged"), Diff(EQUAL,"ConsoleSceneView
¶<<
¶>>"), Diff(DELETE,"Tagged"), Diff(EQUAL,"ConsoleSceneView test execution time (ms):"), Diff(DELETE,"3"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶"), Diff(DELETE,">>Steps traced since last test:
¶
¶"), Diff(EQUAL,">>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(EQUAL,"ConsoleSceneView,0.0% complete,0.0,"), Diff(INSERT,"5"), Diff(EQUAL,"0.0,"), Diff(DELETE,"No class in project matching name/tag:ConsoleSceneView
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<
¶>>Steps traced since last test:
¶
¶>>TaggedLocatable test execution time (ms):1<<
¶>>Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<
¶>>ConsoleSceneViewGetsBridgeScene test execution time (ms):6<<
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>Steps traced since last test:
¶
¶>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable"), Diff(INSERT,"
¶Preceding test BridgeSceneSingletonFromFactory failed.
¶Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory"), Diff(EQUAL," before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="33" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="69993" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Observables
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:06:58 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<
¶>>ConsoleSceneView test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,
¶Preceding test BridgeSceneSingletonFromFactory failed.
¶Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test"), Diff(INSERT,"Running suite A2Observables"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="34" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="70001" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:01 EDT 2025<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Observables"), Diff(INSERT,"Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test Locatable_IS_A_PropertyListenerRegisterer"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="35" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="70032" type="ConsoleOutput">
    <outputString><![CDATA[>>Locatable_IS_A_PropertyListenerRegisterer test execution time (ms):40<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"Locatable_IS_A_PropertyListenerRegisterer"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):40"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="36" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="70035" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"Locatable_IS_A_PropertyListenerRegisterer"), Diff(DELETE," test execution time (ms):40"), Diff(INSERT,",100.0% complete,0.0,0.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="37" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="70042" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:01 EDT 2025<<
>>Running test LocatablePropertyChangeListenersProperty
<<
>>LocatablePropertyChangeListenersProperty test execution time (ms):2<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test LocatablePropertyChangeListenersProperty
¶<<
¶>>"), Diff(EQUAL,"Locatable"), Diff(DELETE,"_IS_A_"), Diff(EQUAL,"Property"), Diff(INSERT,"Change"), Diff(EQUAL,"Listener"), Diff(DELETE,"Registerer,100.0% complete,0.0,0.0,"), Diff(INSERT,"sProperty test execution time (ms):2<<
¶>>Test Result:
¶LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="38" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="70046" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:01 EDT 2025<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>LocatableInstantiatesPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 19:07:01 EDT 2025<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
>>LocatableAnnouncesPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test Locatable"), Diff(INSERT,"Instantiates"), Diff(EQUAL,"PropertyChange"), Diff(DELETE,"ListenersProperty
¶<<
¶>>LocatablePropertyChangeListenersProperty"), Diff(INSERT,"Event
¶<<
¶>>LocatableInstantiatesPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test LocatableAnnouncesPropertyChangeEvent
¶<<
¶>>LocatableAnnouncesPropertyChangeEvent"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"2"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶Locatable"), Diff(INSERT,"Announces"), Diff(EQUAL,"PropertyChange"), Diff(DELETE,"ListenersProperty"), Diff(INSERT,"Event"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="39" _type="ConsoleOutput" date="Wed Jul 09 19:07:01 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="70065" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:01 EDT 2025<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>BoundedShapeInstantiatesPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 19:07:01 EDT 2025<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
>>BoundedShapeAnnouncesPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test "), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eInstantiatesPropertyChangeEvent
¶<<
¶>>"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eInstantiatesPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test "), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eAnnouncesPropertyChangeEvent
¶<<
¶>>"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eAnnouncesPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="40" _type="ConsoleOutput" date="Wed Jul 09 19:07:04 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="72639" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite BridgeSceneScroll
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test BoundedShapeInstantiatesPropertyChangeEvent
¶<<
¶>>BoundedShapeInstantiatesPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 19:07:01 EDT 2025<<
¶>>Running test BoundedShapeAnnouncesPropertyChangeEvent
¶<<
¶>>BoundedShapeAnnouncesPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(INSERT,"Running suite BridgeSceneScroll"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="41" _type="ConsoleOutput" date="Wed Jul 09 19:07:04 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="72647" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:04 EDT 2025<<
>>Running test BridgeSceneScrollMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>Wed Jul 09 19:07:04 EDT 2025<<
¶"), Diff(EQUAL,">>Running "), Diff(DELETE,"sui"), Diff(EQUAL,"te"), Diff(INSERT,"st"), Diff(EQUAL," BridgeSceneScroll"), Diff(INSERT,"MethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="42" _type="ConsoleOutput" date="Wed Jul 09 19:07:04 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="72661" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneScrollMethodDefined test execution time (ms):22<<
>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:07:04 EDT 2025<<
¶>>Running test BridgeSceneScrollMethodDefined
¶<<"), Diff(INSERT,"BridgeSceneScrollMethodDefined test execution time (ms):22<<
¶>>Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="43" _type="ConsoleOutput" date="Wed Jul 09 19:07:04 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="72669" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Wed Jul 09 19:07:04 EDT 2025<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneScrollMethodDefined"), Diff(DELETE," test execution time (ms):22<<
¶>>Steps traced since last test:"), Diff(INSERT,",0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
¶<<
¶>>Wed Jul 09 19:07:04 EDT 2025<<
¶>>Running test BridgeSceneArthurScrollLeftArmTestCase
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="44" _type="ExceptionCommand" date="Wed Jul 09 19:07:04 EDT 2025" starttimestamp="1752102351757" timestamp="72673" type="Exception">
    <exceptionString><![CDATA[java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type BridgeSceneImpl is inconsistent
	The method scroll(String, int, int) of type BridgeSceneImpl must override or implement a supertype method
	The method scroll(String, int, int) is undefined for the type Avatar

	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:19)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="45" _type="ConsoleOutput" date="Wed Jul 09 19:07:04 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="72691" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type BridgeSceneImpl is inconsistent
	The method scroll(String, int, int) of type BridgeSceneImpl must override or implement a supertype method
	The method scroll(String, int, int) is undefined for the type Avatar

	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:19)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:17: Missing signature @scroll:"), Diff(INSERT,"java.lang.Error: Unresolved compilation problems: 
¶	The hierarchy of the type BridgeSceneImpl is inconsistent
¶	The method scroll(String, int, int) of type BridgeSceneImpl must override or implement a supertype method
¶	The method scroll(String, "), Diff(EQUAL,"int"), Diff(DELETE,";"), Diff(INSERT,", "), Diff(EQUAL,"int"), Diff(DELETE,"->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
¶<<
¶>>Wed Jul 09 19:07:04 EDT 2025<<
¶>>Running test BridgeSceneArthurScrollLeftArmTestCase
¶<<"), Diff(INSERT,") is undefined for the type Avatar
¶
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:19)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
¶	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
¶	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
¶	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="46" _type="ExceptionCommand" date="Wed Jul 09 19:07:04 EDT 2025" starttimestamp="1752102351757" timestamp="72726" type="Exception">
    <exceptionString><![CDATA[java.lang.NullPointerException
	at java.base/java.util.Hashtable.put(Hashtable.java:476)
	at util.models.Hashcodetable.put(Hashcodetable.java:15)
	at grader.basics.project.BasicProjectIntrospection.createTimingOutProxy(BasicProjectIntrospection.java:2341)
	at grader.basics.project.BasicProjectIntrospection.createProxy(BasicProjectIntrospection.java:2356)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2503)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2466)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2260)
	at grader.basics.project.BasicProjectIntrospection.createOrGetLastInstance(BasicProjectIntrospection.java:2254)
	at gradingTools.shared.testcases.ProxyTest.createOrGetLastRootProxy(ProxyTest.java:70)
	at gradingTools.shared.testcases.ProxyTest.create(ProxyTest.java:181)
	at gradingTools.comp401f16.assignment5.testcases.move.arthur.BridgeSceneMoveTestCase.create(BridgeSceneMoveTestCase.java:28)
	at gradingTools.shared.testcases.ProxyTest.doProxyTest(ProxyTest.java:253)
	at gradingTools.shared.testcases.ProxyTest.doTest(ProxyTest.java:262)
	at gradingTools.comp401f16.assignment5.testcases.move.arthur.BridgeSceneArthurMoveLeftArmTestCase.doTest(BridgeSceneArthurMoveLeftArmTestCase.java:32)
	at gradingTools.comp401f16.assignment6.testcases.scroll.BridgeSceneArthurScrollLeftArmTestCase.doTest(BridgeSceneArthurScrollLeftArmTestCase.java:26)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at jdk.internal.reflect.GeneratedMethodAccessor29.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="47" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75086" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Style
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"java.lang.Error: Unresolved compilation problems: 
¶	The hierarchy of the type BridgeSceneImpl is inconsistent
¶	The method scroll(String, int, int) of type BridgeSceneImpl must override or implement a supertype method
¶	The method scroll(String, int, int) is undefined for the type Avatar
¶
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:19)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
¶	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
¶	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
¶	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)"), Diff(INSERT,">>Running suite A2Style
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="48" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75093" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test A2PackageDeclarations
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test BridgeSceneDynamics
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test BridgeSceneApproachMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Style"), Diff(INSERT,"Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2PackageDeclarations
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeSceneDynamics
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeSceneApproachMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="49" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75109" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneApproachMethodDefined test execution time (ms):21<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2PackageDeclarations
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeSceneDynamics
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):21"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="50" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75115" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE," test execution time (ms):21"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="51" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75121" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test BridgeSceneSayMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Approach"), Diff(INSERT,"Say"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="52" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75137" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSayMethodDefined test execution time (ms):22<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeSceneSayMethodDefined
¶"), Diff(INSERT,"BridgeSceneSayMethodDefined test execution time (ms):22"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="53" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75141" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test BridgeScenePassedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSayMethodDefined"), Diff(DELETE," test execution time (ms):22"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="54" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75162" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeScenePassedMethodDefined test execution time (ms):22<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Say"), Diff(INSERT,"Passed"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(INSERT," test execution time (ms):22"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="55" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75170" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test BridgeSceneFailedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeScenePassedMethodDefined"), Diff(DELETE," test execution time (ms):22"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="56" _type="ConsoleOutput" date="Wed Jul 09 19:07:06 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75186" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFailedMethodDefined test execution time (ms):19<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Pass"), Diff(INSERT,"Fail"), Diff(EQUAL,"edMethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(INSERT," test execution time (ms):19"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="57" _type="ExceptionCommand" date="Wed Jul 09 19:07:06 EDT 2025" starttimestamp="1752102351757" timestamp="75202" type="Exception">
    <exceptionString><![CDATA[>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type BridgeSceneImpl is inconsistent
	The method scroll(String, int, int) of type BridgeSceneImpl must override or implement a supertype method
	The method scroll(String, int, int) is undefined for the type Avatar

	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:19)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.NullPointerException
	at java.base/java.util.Hashtable.put(Hashtable.java:476)
	at util.models.Hashcodetable.put(Hashcodetable.java:15)
	at grader.basics.project.BasicProjectIntrospection.createTimingOutProxy(BasicProjectIntrospection.java:2341)
	at grader.basics.project.BasicProjectIntrospection.createProxy(BasicProjectIntrospection.java:2356)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2503)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2466)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2260)
	at gradingTools.shared.testcases.ProxyTest.createRootProxy(ProxyTest.java:49)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.create(BridgeSceneDynamicTestCase.java:247)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.doTest(BridgeSceneDynamicTestCase.java:87)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at jdk.internal.reflect.GeneratedMethodAccessor29.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AJUnitTestContext.getAndPossiblyRunJUnitPassFailTestCase(AJUnitTestContext.java:58)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="58" _type="ConsoleOutput" date="Wed Jul 09 19:07:07 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="75255" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2PackageDeclarations,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test A2SimplifyBooleanExpressions
<<
>>A2SimplifyBooleanExpressions test execution time (ms):1<<
>>Test Result:
A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test A2SimplifyBooleanReturns
<<
>>A2SimplifyBooleanReturns test execution time (ms):0<<
>>Test Result:
A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test A2NoHiddenFields
<<
>>A2NoHiddenFields test execution time (ms):0<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test A2NamingConventions
<<
>>A2NamingConventions test execution time (ms):0<<
>>Test Result:
A2NamingConventions,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
>>Wed Jul 09 19:07:06 EDT 2025<<
>>Running test A2InterfaceAsType
<<
>>A2InterfaceAsType test execution time (ms):0<<
>>Test Result:
A2InterfaceAsType,0.0% complete,0.0,5.0,
Preceding test BridgeSceneDynamics failed.
Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"BridgeSceneFailedMethodDefined test execution time (ms):19"), Diff(INSERT,"Test Result:
¶A2PackageDeclarations,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2SimplifyBooleanExpressions
¶<<
¶>>A2SimplifyBooleanExpressions test execution time (ms):1<<
¶>>Test Result:
¶A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2SimplifyBooleanReturns
¶<<
¶>>A2SimplifyBooleanReturns test execution time (ms):0<<
¶>>Test Result:
¶A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2NoHiddenFields
¶<<
¶>>A2NoHiddenFields test execution time (ms):0<<
¶>>Test Result:
¶A2NoHiddenFields,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2NamingConventions
¶<<
¶>>A2NamingConventions test execution time (ms):0<<
¶>>Test Result:
¶A2NamingConventions,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2InterfaceAsType
¶<<
¶>>A2InterfaceAsType test execution time (ms):0<<
¶>>Test Result:
¶A2InterfaceAsType,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="59" _type="ConsoleOutput" date="Wed Jul 09 19:07:34 EDT 2025" overflow="false" starttimestamp="1752102351757" timestamp="102276" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.style.A2PackageDeclarations@275a99ba . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶A2PackageDeclarations,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2SimplifyBooleanExpressions
¶<<
¶>>A2SimplifyBooleanExpressions test execution time (ms):1<<
¶>>Test Result:
¶A2SimplifyBooleanExpressions,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2SimplifyBooleanReturns
¶<<
¶>>A2SimplifyBooleanReturns test execution time (ms):0<<
¶>>Test Result:
¶A2SimplifyBooleanReturns,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2NoHiddenFields
¶<<
¶>>A2NoHiddenFields test execution time (ms):0<<
¶>>Test Result:
¶A2NoHiddenFields,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2NamingConventions
¶<<
¶>>A2NamingConventions test execution time (ms):0<<
¶>>Test Result:
¶A2NamingConventions,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<
¶>>Wed Jul 09 19:07:06 EDT 2025<<
¶>>Running test A2InterfaceAsType
¶<<
¶>>A2InterfaceAsType test execution time (ms):0<<
¶>>Test Result:
¶A2InterfaceAsType,0.0% complete,0.0,5.0,
¶Preceding test BridgeSceneDynamics failed.
¶Please correct the problems identified by preceding test:BridgeSceneDynamics before running this test
¶<<"), Diff(INSERT,"Re-running test gradingTools.comp301ss21.assignment2.testcases.style.A2PackageDeclarations@275a99ba . Results may change."), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="60" _type="ShellCommand" date="Wed Jul 09 19:07:43 EDT 2025" starttimestamp="1752102351757" timestamp="111892" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="61" _type="ShellCommand" date="Wed Jul 09 19:17:54 EDT 2025" starttimestamp="1752102351757" timestamp="723050" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="62" _type="ShellCommand" date="Wed Jul 09 19:24:51 EDT 2025" starttimestamp="1752102351757" timestamp="1139894" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="65" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 19:34:15 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752102351757" timestamp="1704190" type="Run" />
  <Command __id="66" _type="ShellCommand" date="Wed Jul 09 19:34:18 EDT 2025" starttimestamp="1752102351757" timestamp="1706820" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="67" _type="ShellCommand" date="Wed Jul 09 19:34:54 EDT 2025" starttimestamp="1752102351757" timestamp="1742249" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="68" _type="ShellCommand" date="Wed Jul 09 19:34:55 EDT 2025" starttimestamp="1752102351757" timestamp="1743878" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="70" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:34:59 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="1748018" />
  <Command __id="71" _type="MoveCaretCommand" caretOffset="168" date="Wed Jul 09 19:35:04 EDT 2025" docOffset="408" starttimestamp="1752102351757" timestamp="1752305" />
  <Command __id="72" _type="SelectTextCommand" caretOffset="172" date="Wed Jul 09 19:35:04 EDT 2025" end="172" start="161" starttimestamp="1752102351757" timestamp="1752495" />
  <Command __id="73" _type="CopyCommand" date="Wed Jul 09 19:35:05 EDT 2025" starttimestamp="1752102351757" timestamp="1753354" />
  <Command __id="74" _type="ShellCommand" date="Wed Jul 09 19:35:05 EDT 2025" starttimestamp="1752102351757" timestamp="1753967" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="69" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:34:59 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3326" docExpressionCount="266" docLength="3326" projectName="Assn2" starttimestamp="1752102351757" timestamp="1747758">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\GalahadHead.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape {
    private String fileName = "images/galahad.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public GalahadHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
]]></snapshot>
  </Command>
  <Command __id="75" _type="ShellCommand" date="Wed Jul 09 19:35:35 EDT 2025" starttimestamp="1752102351757" timestamp="1784050" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="77" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:35:49 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="1798127" />
  <Command __id="78" _type="MoveCaretCommand" caretOffset="425" date="Wed Jul 09 19:35:56 EDT 2025" docOffset="665" starttimestamp="1752102351757" timestamp="1804271" />
  <Command __id="79" _type="MoveCaretCommand" caretOffset="193" date="Wed Jul 09 19:35:59 EDT 2025" docOffset="433" starttimestamp="1752102351757" timestamp="1807455" />
  <Command __id="80" _type="SelectTextCommand" caretOffset="195" date="Wed Jul 09 19:35:59 EDT 2025" end="195" start="185" starttimestamp="1752102351757" timestamp="1807663" />
  <Command __id="81" _type="SelectTextCommand" caretOffset="195" date="Wed Jul 09 19:35:59 EDT 2025" end="195" start="185" starttimestamp="1752102351757" timestamp="1808134" />
  <Command __id="82" _type="CopyCommand" date="Wed Jul 09 19:36:01 EDT 2025" starttimestamp="1752102351757" timestamp="1810077" />
  <Command __id="83" _type="ShellCommand" date="Wed Jul 09 19:36:06 EDT 2025" starttimestamp="1752102351757" timestamp="1814319" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="84" _type="ShellCommand" date="Wed Jul 09 19:36:17 EDT 2025" starttimestamp="1752102351757" timestamp="1825586" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="85" _type="ShellCommand" date="Wed Jul 09 19:36:46 EDT 2025" starttimestamp="1752102351757" timestamp="1854626" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="76" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:35:49 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3329" docExpressionCount="266" docLength="3329" projectName="Assn2" starttimestamp="1752102351757" timestamp="1797910">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\LancelotHead.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape {
    private String fileName = "images/lancelot.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public LancelotHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
]]></snapshot>
  </Command>
  <Command __id="86" _type="ShellCommand" date="Wed Jul 09 19:37:58 EDT 2025" starttimestamp="1752102351757" timestamp="1927059" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="88" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:38:04 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="1932304" />
  <Command __id="89" _type="MoveCaretCommand" caretOffset="240" date="Wed Jul 09 19:38:05 EDT 2025" docOffset="421" starttimestamp="1752102351757" timestamp="1934143" />
  <Command __id="90" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:38:07 EDT 2025" starttimestamp="1752102351757" timestamp="1935429" />
  <Command __id="91" _type="SelectTextCommand" caretOffset="240" date="Wed Jul 09 19:38:07 EDT 2025" end="240" start="0" starttimestamp="1752102351757" timestamp="1936174" />
  <Command __id="92" _type="CopyCommand" date="Wed Jul 09 19:38:09 EDT 2025" starttimestamp="1752102351757" timestamp="1937485" />
  <Command __id="93" _type="ShellCommand" date="Wed Jul 09 19:38:09 EDT 2025" starttimestamp="1752102351757" timestamp="1937715" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="94" _type="ShellCommand" date="Wed Jul 09 19:39:20 EDT 2025" starttimestamp="1752102351757" timestamp="2008568" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="95" _type="ShellCommand" date="Wed Jul 09 19:39:22 EDT 2025" starttimestamp="1752102351757" timestamp="2011037" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="96" _type="ShellCommand" date="Wed Jul 09 19:39:27 EDT 2025" starttimestamp="1752102351757" timestamp="2015243" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="97" _type="MoveCaretCommand" caretOffset="240" date="Wed Jul 09 19:39:28 EDT 2025" docOffset="421" starttimestamp="1752102351757" timestamp="2016504" />
  <Command __id="87" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:38:03 EDT 2025" docASTNodeCount="62" docActiveCodeLength="421" docExpressionCount="47" docLength="421" projectName="Assn2" starttimestamp="1752102351757" timestamp="1932117">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Angle.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable{
    RotateLine getLeftLine();
    RotateLine getRightLine();
}
]]></snapshot>
  </Command>
  <Command __id="98" _type="SelectTextCommand" caretOffset="0" date="Wed Jul 09 19:39:29 EDT 2025" end="240" start="0" starttimestamp="1752102351757" timestamp="2017773" />
  <Command __id="100" _type="PasteCommand" date="Wed Jul 09 19:39:31 EDT 2025" starttimestamp="1752102351757" timestamp="2019348" />
  <Command __id="101" _type="EclipseCommand" commandID="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" date="Wed Jul 09 19:39:39 EDT 2025" starttimestamp="1752102351757" timestamp="2027804" />
  <Command __id="102" _type="WebVisitCommand" date="Wed Jul 09 19:39:39 EDT 2025" numVisits="1" searchString="Java Code Checkstyle Compliance" starttimestamp="1752102351757" timestamp="2027818" url="https://chatgpt.com/c/686efd45-7fe8-8005-a924-0934976ea62e" />
  <Command __id="103" _type="EclipseCommand" commandID="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" date="Wed Jul 09 19:39:45 EDT 2025" starttimestamp="1752102351757" timestamp="2033619" />
  <DocumentChange __id="99" _type="Insert" date="Wed Jul 09 19:39:31 EDT 2025" docASTNodeCount="133" docActiveCodeLength="874" docExpressionCount="96" docLength="874" length="453" offset="398" starttimestamp="1752102351757" timestamp="2019333">
    <text><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}
]]></text>
  </DocumentChange>
  <Command __id="104" _type="MoveCaretCommand" caretOffset="294" date="Wed Jul 09 19:39:53 EDT 2025" docOffset="872" starttimestamp="1752102351757" timestamp="2042030" />
  <Command __id="105" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:39:54 EDT 2025" starttimestamp="1752102351757" timestamp="2042367" />
  <DocumentChange __id="106" _type="Delete" date="Wed Jul 09 19:39:55 EDT 2025" docASTNodeCount="1" docActiveCodeLength="0" docExpressionCount="0" docLength="0" endLine="29" length="874" offset="0" startLine="0" starttimestamp="1752102351757" timestamp="2043499">
    <text><![CDATA[package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable{
    RotateLine getLeftLine();
    RotateLipackage mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}
ne getRightLine();
}
]]></text>
  </DocumentChange>
  <Command __id="107" _type="EclipseCommand" commandID="org.eclipse.ui.edit.delete" date="Wed Jul 09 19:39:55 EDT 2025" starttimestamp="1752102351757" timestamp="2043509" />
  <Command __id="109" _type="PasteCommand" date="Wed Jul 09 19:39:56 EDT 2025" starttimestamp="1752102351757" timestamp="2044254" />
  <DocumentChange __id="108" _type="Insert" date="Wed Jul 09 19:39:55 EDT 2025" docASTNodeCount="71" docActiveCodeLength="453" docExpressionCount="50" docLength="453" length="453" offset="0" starttimestamp="1752102351757" timestamp="2044238">
    <text><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}
]]></text>
  </DocumentChange>
  <Command __id="110" _type="MoveCaretCommand" caretOffset="451" date="Wed Jul 09 19:39:59 EDT 2025" docOffset="451" starttimestamp="1752102351757" timestamp="2047902" />
  <Command __id="111" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:40:00 EDT 2025" starttimestamp="1752102351757" timestamp="2048735" />
  <Command __id="112" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:40:00 EDT 2025" starttimestamp="1752102351757" timestamp="2048835" />
  <Command __id="113" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:40:00 EDT 2025" starttimestamp="1752102351757" timestamp="2048902" />
  <Command __id="114" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:40:09 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3326" docExpressionCount="266" docLength="3326" projectName="Assn2" starttimestamp="1752102351757" timestamp="2057572">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\GalahadHead.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;import java.beans.PropertyChangeEvent;import java.beans.PropertyChangeListener;import java.util.ArrayList;import java.util.List;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.IMAGE_PATTERN)@StructurePattern(StructurePatternNames.IMAGE_PATTERN)public class GalahadHead implements ImageShape {    private String fileName = "images/galahad.jpg";    private int x, y, width = 50, height = 50;    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();    public GalahadHead() {    }    @Override    public String getImageFileName() {        return fileName;    }    @Override    public void setImageFileName(final String fileName) {        String oldValue = this.fileName;        this.fileName = fileName;        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);        for (PropertyChangeListener listener : propertyChangeListeners) {            listener.propertyChange(event);        }    }    @Override    public int getX() {        return x;    }    @Override    public void setX(final int value) {        int oldValue = this.x;        this.x = value;        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);        for (PropertyChangeListener listener : propertyChangeListeners) {            listener.propertyChange(event);        }    }    @Override    public int getY() {        return y;    }    @Override    public void setY(final int value) {        int oldValue = this.y;        this.y = value;        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);        for (PropertyChangeListener listener : propertyChangeListeners) {            listener.propertyChange(event);        }    }    @Override    public int getWidth() {        return width;    }    @Override    public void setWidth(final int width) {        int oldValue = this.width;        this.width = width;        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);        for (PropertyChangeListener listener : propertyChangeListeners) {            listener.propertyChange(event);        }    }    @Override    public int getHeight() {        return height;    }    @Override    public void setHeight(final int height) {        int oldValue = this.height;        this.height = height;        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);        for (PropertyChangeListener listener : propertyChangeListeners) {            listener.propertyChange(event);        }    }    @Override    public List<PropertyChangeListener> getPropertyChangeListeners() {        return new ArrayList<>(propertyChangeListeners);    }    @Override    public void addPropertyChangeListener(final PropertyChangeListener listener) {        if (listener != null && !propertyChangeListeners.contains(listener)) {            propertyChangeListeners.add(listener);        }    }    @Override    public void removePropertyChangeListener(final PropertyChangeListener listener) {        propertyChangeListeners.remove(listener);    }}")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="115" _type="SelectTextCommand" caretOffset="172" date="Wed Jul 09 19:40:09 EDT 2025" end="172" start="161" starttimestamp="1752102351757" timestamp="2057663" />
  <Command __id="116" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:40:10 EDT 2025" docASTNodeCount="71" docActiveCodeLength="453" docExpressionCount="50" docLength="453" projectName="Assn2" starttimestamp="1752102351757" timestamp="2058718">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Angle.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;
"), Diff(INSERT,"
"), Diff(EQUAL,"import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;"), Diff(DELETE,"   "), Diff(INSERT,"
"), Diff(EQUAL,"
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable"), Diff(INSERT," "), Diff(EQUAL,"{
    RotateLine getLeftLine();
    RotateLine getRightLine();
"), Diff(INSERT,"    void move(int x, int y);
"), Diff(EQUAL,"}
")]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}
]]></snapshot>
  </Command>
  <Command __id="117" _type="MoveCaretCommand" caretOffset="451" date="Wed Jul 09 19:40:10 EDT 2025" docOffset="451" starttimestamp="1752102351757" timestamp="2058867" />
  <Command __id="118" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:40:12 EDT 2025" docASTNodeCount="95" docActiveCodeLength="813" docExpressionCount="56" docLength="1205" projectName="Assn2" starttimestamp="1752102351757" timestamp="2061226">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\main\RunSS25A2Tests.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package main;

import grader.basics.execution.BasicProjectExecution;
import gradingTools.comp301ss24.assignment2.SS24Assignment2Suite;
import trace.grader.basics.GraderBasicsTraceUtility;
public class RunSS25A2Tests {
	 private static final int MAX_PRINTED_TRACES = 600;
		 private static final int MAX_TRACES         = 2000;
		 private static final int PROCESS_TIMEOUT_S  = 5;
		public static void main(String[] args) {
			// if you set this to false, grader steps will not be traced
			GraderBasicsTraceUtility.setTracerShowInfo(true);	
			// if you set this to false, all grader steps will be traced,
			// not just the ones that failed		
			GraderBasicsTraceUtility.setBufferTracedMessages(true);
			// Change this number if a test trace gets longer than 600 and is clipped
			GraderBasicsTraceUtility.setMaxPrintedTraces(MAX_PRINTED_TRACES);
			// Change this number if all traces together are longer than 2000
			GraderBasicsTraceUtility.setMaxTraces(MAX_TRACES);
			// Change this number if your process times out prematurely
			BasicProjectExecution.setProcessTimeOut(PROCESS_TIMEOUT_S);
			// You need to always call such a method
		SS24Assignment2Suite.main(args);
	}
}
]]></snapshot>
  </Command>
  <Command __id="119" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:40:13 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2061288" />
  <Command __id="120" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:40:13 EDT 2025" docASTNodeCount="71" docActiveCodeLength="453" docExpressionCount="50" docLength="453" projectName="Assn2" starttimestamp="1752102351757" timestamp="2062034">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Angle.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}
")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="121" _type="MoveCaretCommand" caretOffset="451" date="Wed Jul 09 19:40:13 EDT 2025" docOffset="451" starttimestamp="1752102351757" timestamp="2062086" />
  <Command __id="123" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:40:17 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2065883" />
  <Command __id="124" _type="MoveCaretCommand" caretOffset="471" date="Wed Jul 09 19:40:19 EDT 2025" docOffset="711" starttimestamp="1752102351757" timestamp="2067543" />
  <Command __id="125" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:40:20 EDT 2025" starttimestamp="1752102351757" timestamp="2068364" />
  <Command __id="126" _type="CopyCommand" date="Wed Jul 09 19:40:20 EDT 2025" starttimestamp="1752102351757" timestamp="2069238" />
  <Command __id="127" _type="CopyCommand" date="Wed Jul 09 19:40:21 EDT 2025" starttimestamp="1752102351757" timestamp="2069472" />
  <Command __id="128" _type="ShellCommand" date="Wed Jul 09 19:40:21 EDT 2025" starttimestamp="1752102351757" timestamp="2070165" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="129" _type="ShellCommand" date="Wed Jul 09 19:40:36 EDT 2025" starttimestamp="1752102351757" timestamp="2085059" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="122" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:40:17 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3322" docExpressionCount="266" docLength="3322" projectName="Assn2" starttimestamp="1752102351757" timestamp="2065712">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ArthurHead.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
]]></snapshot>
  </Command>
  <Command __id="130" _type="MoveCaretCommand" caretOffset="471" date="Wed Jul 09 19:40:39 EDT 2025" docOffset="711" starttimestamp="1752102351757" timestamp="2087558" />
  <Command __id="131" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:40:40 EDT 2025" starttimestamp="1752102351757" timestamp="2088320" />
  <DocumentChange __id="132" _type="Delete" date="Wed Jul 09 19:40:41 EDT 2025" docASTNodeCount="1" docActiveCodeLength="0" docExpressionCount="0" docLength="0" endLine="112" length="3322" offset="0" startLine="0" starttimestamp="1752102351757" timestamp="2089377">
    <text><![CDATA[package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }
    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
]]></text>
  </DocumentChange>
  <Command __id="133" _type="EclipseCommand" commandID="org.eclipse.ui.edit.delete" date="Wed Jul 09 19:40:41 EDT 2025" starttimestamp="1752102351757" timestamp="2089389" />
  <Command __id="135" _type="PasteCommand" date="Wed Jul 09 19:40:42 EDT 2025" starttimestamp="1752102351757" timestamp="2090672" />
  <Command __id="136" _type="MoveCaretCommand" caretOffset="3431" date="Wed Jul 09 19:40:44 EDT 2025" docOffset="3431" starttimestamp="1752102351757" timestamp="2093215" />
  <Command __id="137" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:40:45 EDT 2025" starttimestamp="1752102351757" timestamp="2094193" />
  <Command __id="138" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:40:46 EDT 2025" starttimestamp="1752102351757" timestamp="2094289" />
  <Command __id="139" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:40:46 EDT 2025" starttimestamp="1752102351757" timestamp="2094385" />
  <Command __id="140" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:41:26 EDT 2025" starttimestamp="1752102351757" timestamp="2134393" />
  <Command __id="141" _type="MoveCaretCommand" caretOffset="667" date="Wed Jul 09 19:41:28 EDT 2025" docOffset="667" starttimestamp="1752102351757" timestamp="2136445" />
  <Command __id="142" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:41:29 EDT 2025" starttimestamp="1752102351757" timestamp="2137568" />
  <Command __id="143" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:41:29 EDT 2025" starttimestamp="1752102351757" timestamp="2138064" />
  <Command __id="144" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:41:30 EDT 2025" starttimestamp="1752102351757" timestamp="2138641" />
  <Command __id="145" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:41:30 EDT 2025" starttimestamp="1752102351757" timestamp="2138909" />
  <Command __id="146" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:41:30 EDT 2025" starttimestamp="1752102351757" timestamp="2139093" />
  <Command __id="147" _type="MoveCaretCommand" caretOffset="691" date="Wed Jul 09 19:41:32 EDT 2025" docOffset="691" starttimestamp="1752102351757" timestamp="2140519" />
  <Command __id="148" _type="MoveCaretCommand" caretOffset="2559" date="Wed Jul 09 19:41:45 EDT 2025" docOffset="2559" starttimestamp="1752102351757" timestamp="2153461" />
  <Command __id="149" _type="ShellCommand" date="Wed Jul 09 19:41:49 EDT 2025" starttimestamp="1752102351757" timestamp="2157513" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="150" _type="ShellCommand" date="Wed Jul 09 19:41:50 EDT 2025" starttimestamp="1752102351757" timestamp="2158956" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="151" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:41:50 EDT 2025" starttimestamp="1752102351757" timestamp="2158966" />
  <Command __id="152" _type="EclipseCommand" commandID="org.eclipse.ui.project.cleanAction" date="Wed Jul 09 19:41:50 EDT 2025" starttimestamp="1752102351757" timestamp="2158984" />
  <Command __id="153" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:41:50 EDT 2025" starttimestamp="1752102351757" timestamp="2159116" />
  <Command __id="154" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:41:51 EDT 2025" starttimestamp="1752102351757" timestamp="2159340" />
  <Command __id="155" _type="MoveCaretCommand" caretOffset="691" date="Wed Jul 09 19:42:00 EDT 2025" docOffset="691" starttimestamp="1752102351757" timestamp="2169184" />
  <Command __id="156" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:42:01 EDT 2025" starttimestamp="1752102351757" timestamp="2170211" />
  <Command __id="157" _type="SelectTextCommand" caretOffset="3433" date="Wed Jul 09 19:42:02 EDT 2025" end="3433" start="0" starttimestamp="1752102351757" timestamp="2171039" />
  <Command __id="158" _type="MoveCaretCommand" caretOffset="757" date="Wed Jul 09 19:42:14 EDT 2025" docOffset="757" starttimestamp="1752102351757" timestamp="2182989" />
  <Command __id="159" _type="MoveCaretCommand" caretOffset="691" date="Wed Jul 09 19:42:26 EDT 2025" docOffset="691" starttimestamp="1752102351757" timestamp="2194999" />
  <Command __id="160" _type="ShellCommand" date="Wed Jul 09 19:42:36 EDT 2025" starttimestamp="1752102351757" timestamp="2204925" type="ECLIPSE_LOST_FOCUS" />
  <DocumentChange __id="134" _type="Insert" date="Wed Jul 09 19:40:42 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3433" docExpressionCount="266" docLength="3433" length="3433" offset="0" starttimestamp="1752102351757" timestamp="2090648">
    <text><![CDATA[package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
]]></text>
  </DocumentChange>
  <Command __id="161" _type="ShellCommand" date="Wed Jul 09 19:42:56 EDT 2025" starttimestamp="1752102351757" timestamp="2224701" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="163" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:43:13 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2241460" />
  <Command __id="164" _type="MoveCaretCommand" caretOffset="274" date="Wed Jul 09 19:43:15 EDT 2025" docOffset="421" starttimestamp="1752102351757" timestamp="2243493" />
  <Command __id="165" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:43:16 EDT 2025" starttimestamp="1752102351757" timestamp="2244261" />
  <Command __id="166" _type="SelectTextCommand" caretOffset="274" date="Wed Jul 09 19:43:17 EDT 2025" end="274" start="0" starttimestamp="1752102351757" timestamp="2245478" />
  <Command __id="167" _type="CopyCommand" date="Wed Jul 09 19:43:19 EDT 2025" starttimestamp="1752102351757" timestamp="2247734" />
  <Command __id="168" _type="ShellCommand" date="Wed Jul 09 19:43:19 EDT 2025" starttimestamp="1752102351757" timestamp="2247946" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="169" _type="ShellCommand" date="Wed Jul 09 19:43:30 EDT 2025" starttimestamp="1752102351757" timestamp="2259102" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="162" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:43:13 EDT 2025" docASTNodeCount="60" docActiveCodeLength="421" docExpressionCount="43" docLength="421" projectName="Assn2" starttimestamp="1752102351757" timestamp="2241302">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ImageShape.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import mp.shapes.BoundedShape;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends BoundedShape {
    String getImageFileName();
    void setImageFileName(final String fileName);
}
]]></snapshot>
  </Command>
  <Command __id="170" _type="MoveCaretCommand" caretOffset="274" date="Wed Jul 09 19:43:31 EDT 2025" docOffset="421" starttimestamp="1752102351757" timestamp="2260142" />
  <Command __id="171" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:43:32 EDT 2025" starttimestamp="1752102351757" timestamp="2260708" />
  <Command __id="173" _type="PasteCommand" date="Wed Jul 09 19:43:33 EDT 2025" starttimestamp="1752102351757" timestamp="2261737" />
  <Command __id="174" _type="MoveCaretCommand" caretOffset="460" date="Wed Jul 09 19:43:35 EDT 2025" docOffset="460" starttimestamp="1752102351757" timestamp="2264109" />
  <Command __id="175" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:43:36 EDT 2025" starttimestamp="1752102351757" timestamp="2264837" />
  <Command __id="176" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:43:36 EDT 2025" starttimestamp="1752102351757" timestamp="2264931" />
  <Command __id="177" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:43:36 EDT 2025" starttimestamp="1752102351757" timestamp="2265145" />
  <Command __id="178" _type="ShellCommand" date="Wed Jul 09 19:44:02 EDT 2025" starttimestamp="1752102351757" timestamp="2290486" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="179" _type="ShellCommand" date="Wed Jul 09 19:44:08 EDT 2025" starttimestamp="1752102351757" timestamp="2297137" type="ECLIPSE_GAINED_FOCUS" />
  <DocumentChange __id="172" _type="Replace" date="Wed Jul 09 19:43:33 EDT 2025" docASTNodeCount="65" docActiveCodeLength="461" docExpressionCount="43" docLength="461" endLine="14" insertionLength="461" int_docASTNodeCount="1" int_docActiveCodeLength="0" int_docExpressionCount="0" int_docLength="0" length="421" offset="0" startLine="0" starttimestamp="1752102351757" timestamp="2261729">
    <deletedText><![CDATA[package mp.bridge;

import mp.shapes.BoundedShape;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends BoundedShape {
    String getImageFileName();
    void setImageFileName(final String fileName);
}
]]></deletedText>
    <insertedText><![CDATA[package mp.bridge;

import mp.shapes.BoundedShape;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public abstract class ImageShape implements BoundedShape {
    public abstract String getImageFileName();
    public abstract void setImageFileName(final String fileName);
}
]]></insertedText>
  </DocumentChange>
  <Command __id="180" _type="MoveCaretCommand" caretOffset="460" date="Wed Jul 09 19:44:13 EDT 2025" docOffset="460" starttimestamp="1752102351757" timestamp="2302172" />
  <Command __id="181" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:44:21 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3433" docExpressionCount="266" docLength="3433" projectName="Assn2" starttimestamp="1752102351757" timestamp="2309588">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ArthurHead.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"import java.beans.PropertyChangeEvent;"), Diff(INSERT,"
"), Diff(EQUAL,"import java.beans.PropertyChangeListener;"), Diff(INSERT,"
"), Diff(EQUAL,"import java.util.ArrayList;"), Diff(INSERT,"
"), Diff(EQUAL,"import java.util.List;"), Diff(INSERT,"
"), Diff(EQUAL,"import tags301.Comp301Tags;"), Diff(INSERT,"
"), Diff(EQUAL,"import util.annotations.StructurePattern;"), Diff(INSERT,"
"), Diff(EQUAL,"import util.annotations.StructurePatternNames;"), Diff(INSERT,"
"), Diff(EQUAL,"import util.annotations.Tags;"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"@Tags(Comp301Tags.IMAGE_PATTERN)"), Diff(INSERT,"
"), Diff(EQUAL,"@StructurePattern(StructurePatternNames.IMAGE_PATTERN)"), Diff(INSERT,"
"), Diff(EQUAL,"public class ArthurHead "), Diff(DELETE,"implem"), Diff(INSERT,"ext"), Diff(EQUAL,"en"), Diff(DELETE,"t"), Diff(INSERT,"d"), Diff(EQUAL,"s ImageShape {"), Diff(INSERT,"
"), Diff(EQUAL,"    private String fileName = "images/arthur.jpg";"), Diff(INSERT,"
"), Diff(EQUAL,"    private int x, y, width = 50, height = 50;"), Diff(INSERT,"
"), Diff(EQUAL,"    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    public ArthurHead() {"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public String getImageFileName() {"), Diff(INSERT,"
"), Diff(EQUAL,"        return fileName;"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void setImageFileName(final String fileName) {"), Diff(INSERT,"
"), Diff(EQUAL,"        String oldValue = this.fileName;"), Diff(INSERT,"
"), Diff(EQUAL,"        this.fileName = fileName;"), Diff(INSERT,"
"), Diff(EQUAL,"        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);"), Diff(INSERT,"
"), Diff(EQUAL,"        for (PropertyChangeListener listener : propertyChangeListeners) {"), Diff(INSERT,"
"), Diff(EQUAL,"            listener.propertyChange(event);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public int getX() {"), Diff(INSERT,"
"), Diff(EQUAL,"        return x;"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void setX(final int value) {"), Diff(INSERT,"
"), Diff(EQUAL,"        int oldValue = this.x;"), Diff(INSERT,"
"), Diff(EQUAL,"        this.x = value;"), Diff(INSERT,"
"), Diff(EQUAL,"        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);"), Diff(INSERT,"
"), Diff(EQUAL,"        for (PropertyChangeListener listener : propertyChangeListeners) {"), Diff(INSERT,"
"), Diff(EQUAL,"            listener.propertyChange(event);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public int getY() {"), Diff(INSERT,"
"), Diff(EQUAL,"        return y;"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void setY(final int value) {"), Diff(INSERT,"
"), Diff(EQUAL,"        int oldValue = this.y;"), Diff(INSERT,"
"), Diff(EQUAL,"        this.y = value;"), Diff(INSERT,"
"), Diff(EQUAL,"        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);"), Diff(INSERT,"
"), Diff(EQUAL,"        for (PropertyChangeListener listener : propertyChangeListeners) {"), Diff(INSERT,"
"), Diff(EQUAL,"            listener.propertyChange(event);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public int getWidth() {"), Diff(INSERT,"
"), Diff(EQUAL,"        return width;"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void setWidth(final int width) {"), Diff(INSERT,"
"), Diff(EQUAL,"        int oldValue = this.width;"), Diff(INSERT,"
"), Diff(EQUAL,"        this.width = width;"), Diff(INSERT,"
"), Diff(EQUAL,"        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);"), Diff(INSERT,"
"), Diff(EQUAL,"        for (PropertyChangeListener listener : propertyChangeListeners) {"), Diff(INSERT,"
"), Diff(EQUAL,"            listener.propertyChange(event);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public int getHeight() {"), Diff(INSERT,"
"), Diff(EQUAL,"        return height;"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void setHeight(final int height) {"), Diff(INSERT,"
"), Diff(EQUAL,"        int oldValue = this.height;"), Diff(INSERT,"
"), Diff(EQUAL,"        this.height = height;"), Diff(INSERT,"
"), Diff(EQUAL,"        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);"), Diff(INSERT,"
"), Diff(EQUAL,"        for (PropertyChangeListener listener : propertyChangeListeners) {"), Diff(INSERT,"
"), Diff(EQUAL,"            listener.propertyChange(event);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public List<PropertyChangeListener> getPropertyChangeListeners() {"), Diff(INSERT,"
"), Diff(EQUAL,"        return new ArrayList<>(propertyChangeListeners);"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void addPropertyChangeListener(final PropertyChangeListener listener) {"), Diff(INSERT,"
"), Diff(EQUAL,"        if (listener != null && !propertyChangeListeners.contains(listener)) {"), Diff(INSERT,"
"), Diff(EQUAL,"            propertyChangeListeners.add(listener);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void removePropertyChangeListener(final PropertyChangeListener listener) {"), Diff(INSERT,"
"), Diff(EQUAL,"        propertyChangeListeners.remove(listener);"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(INSERT,"
"), Diff(EQUAL,"}"), Diff(INSERT,"
"), Diff(EQUAL,"")]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
]]></snapshot>
  </Command>
  <Command __id="182" _type="MoveCaretCommand" caretOffset="691" date="Wed Jul 09 19:44:21 EDT 2025" docOffset="691" starttimestamp="1752102351757" timestamp="2309690" />
  <Command __id="183" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:12 EDT 2025" docASTNodeCount="81" docActiveCodeLength="496" docExpressionCount="52" docLength="496" projectName="Assn2" starttimestamp="1752102351757" timestamp="2360936">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Avatar.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import mp.shapes.Moveable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable, Scrollable {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
    void scale(double factor);
}
]]></snapshot>
  </Command>
  <Command __id="184" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:45:12 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2361104" />
  <Command __id="186" _type="MoveCaretCommand" caretOffset="691" date="Wed Jul 09 19:45:15 EDT 2025" docOffset="691" starttimestamp="1752102351757" timestamp="2363544" />
  <Command __id="185" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:15 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3433" docExpressionCount="266" docLength="3433" projectName="Assn2" starttimestamp="1752102351757" timestamp="2363476">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ArthurHead.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="187" _type="SelectTextCommand" caretOffset="343" date="Wed Jul 09 19:45:16 EDT 2025" end="381" start="343" starttimestamp="1752102351757" timestamp="2365205" />
  <Command __id="188" _type="MoveCaretCommand" caretOffset="343" date="Wed Jul 09 19:45:17 EDT 2025" docOffset="343" starttimestamp="1752102351757" timestamp="2365973" />
  <Command __id="189" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:20 EDT 2025" docASTNodeCount="65" docActiveCodeLength="461" docExpressionCount="43" docLength="461" projectName="Assn2" starttimestamp="1752102351757" timestamp="2368393">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ImageShape.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;import mp.shapes.BoundedShape;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.BOUNDED_SHAPE)@StructurePattern(StructurePatternNames.IMAGE_PATTERN)public "), Diff(DELETE,"interface"), Diff(INSERT,"abstract class"), Diff(EQUAL," ImageShape "), Diff(DELETE,"ext"), Diff(INSERT,"implem"), Diff(EQUAL,"en"), Diff(DELETE,"d"), Diff(INSERT,"t"), Diff(EQUAL,"s BoundedShape {    "), Diff(INSERT,"public abstract "), Diff(EQUAL,"String getImageFileName();    "), Diff(INSERT,"public abstract "), Diff(EQUAL,"void setImageFileName(final String fileName);}")]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import mp.shapes.BoundedShape;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public abstract class ImageShape implements BoundedShape {
    public abstract String getImageFileName();
    public abstract void setImageFileName(final String fileName);
}
]]></snapshot>
  </Command>
  <Command __id="190" _type="MoveCaretCommand" caretOffset="460" date="Wed Jul 09 19:45:20 EDT 2025" docOffset="460" starttimestamp="1752102351757" timestamp="2368448" />
  <Command __id="191" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:23 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3433" docExpressionCount="266" docLength="3433" projectName="Assn2" starttimestamp="1752102351757" timestamp="2371941">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ArthurHead.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="192" _type="MoveCaretCommand" caretOffset="343" date="Wed Jul 09 19:45:23 EDT 2025" docOffset="343" starttimestamp="1752102351757" timestamp="2372018" />
  <Command __id="193" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:24 EDT 2025" docASTNodeCount="71" docActiveCodeLength="453" docExpressionCount="50" docLength="453" projectName="Assn2" starttimestamp="1752102351757" timestamp="2372599">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Angle.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.RotateLine;
import mp.shapes.Moveable;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable {
    RotateLine getLeftLine();
    RotateLine getRightLine();
    void move(int x, int y);
}
")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="194" _type="MoveCaretCommand" caretOffset="451" date="Wed Jul 09 19:45:24 EDT 2025" docOffset="451" starttimestamp="1752102351757" timestamp="2372657" />
  <Command __id="195" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:25 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3433" docExpressionCount="266" docLength="3433" projectName="Assn2" starttimestamp="1752102351757" timestamp="2373408">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ArthurHead.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="196" _type="MoveCaretCommand" caretOffset="343" date="Wed Jul 09 19:45:25 EDT 2025" docOffset="343" starttimestamp="1752102351757" timestamp="2373506" />
  <Command __id="197" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:26 EDT 2025" docASTNodeCount="65" docActiveCodeLength="461" docExpressionCount="43" docLength="461" projectName="Assn2" starttimestamp="1752102351757" timestamp="2375220">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ImageShape.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;import mp.shapes.BoundedShape;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.BOUNDED_SHAPE)@StructurePattern(StructurePatternNames.IMAGE_PATTERN)public abstract class ImageShape implements BoundedShape {    public abstract String getImageFileName();    public abstract void setImageFileName(final String fileName);}")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="198" _type="MoveCaretCommand" caretOffset="460" date="Wed Jul 09 19:45:27 EDT 2025" docOffset="460" starttimestamp="1752102351757" timestamp="2375340" />
  <Command __id="200" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:45:31 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2379557" />
  <Command __id="201" _type="MoveCaretCommand" caretOffset="348" date="Wed Jul 09 19:45:36 EDT 2025" docOffset="495" starttimestamp="1752102351757" timestamp="2385004" />
  <Command __id="202" _type="ShellCommand" date="Wed Jul 09 19:45:39 EDT 2025" starttimestamp="1752102351757" timestamp="2387409" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="203" _type="ShellCommand" date="Wed Jul 09 19:45:41 EDT 2025" starttimestamp="1752102351757" timestamp="2390035" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="199" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:31 EDT 2025" docASTNodeCount="81" docActiveCodeLength="496" docExpressionCount="52" docLength="496" projectName="Assn2" starttimestamp="1752102351757" timestamp="2379449">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Avatar.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;import mp.shapes.Moveable;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.AVATAR)@StructurePattern(StructurePatternNames.BEAN_PATTERN)public interface Avatar extends Moveable, Scrollable {    ImageShape getHead();    StringShape getStringShape();    Angle getArms();    Angle getLegs();    void move(int dx, int dy);    void scale(double factor);}")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="204" _type="MoveCaretCommand" caretOffset="346" date="Wed Jul 09 19:45:43 EDT 2025" docOffset="493" starttimestamp="1752102351757" timestamp="2392229" />
  <Command __id="206" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:45:47 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2396003" />
  <Command __id="207" _type="MoveCaretCommand" caretOffset="84" date="Wed Jul 09 19:45:50 EDT 2025" docOffset="84" starttimestamp="1752102351757" timestamp="2398286" />
  <Command __id="208" _type="MoveCaretCommand" caretOffset="83" date="Wed Jul 09 19:45:53 EDT 2025" docOffset="83" starttimestamp="1752102351757" timestamp="2401549" />
  <Command __id="209" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:45:54 EDT 2025" starttimestamp="1752102351757" timestamp="2402305" />
  <Command __id="210" _type="CopyCommand" date="Wed Jul 09 19:45:54 EDT 2025" starttimestamp="1752102351757" timestamp="2403033" />
  <Command __id="211" _type="ShellCommand" date="Wed Jul 09 19:45:55 EDT 2025" starttimestamp="1752102351757" timestamp="2403585" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="205" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:45:47 EDT 2025" docASTNodeCount="18" docActiveCodeLength="84" docExpressionCount="7" docLength="84" projectName="Assn2" starttimestamp="1752102351757" timestamp="2395852">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\shapes\Moveable.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.shapes;

public interface Moveable {
public void move(int dx, int dy);
}
]]></snapshot>
  </Command>
  <Command __id="212" _type="ShellCommand" date="Wed Jul 09 19:46:00 EDT 2025" starttimestamp="1752102351757" timestamp="2409049" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="214" _type="MoveCaretCommand" caretOffset="346" date="Wed Jul 09 19:46:29 EDT 2025" docOffset="493" starttimestamp="1752102351757" timestamp="2438210" />
  <Command __id="215" _type="ShellCommand" date="Wed Jul 09 19:46:42 EDT 2025" starttimestamp="1752102351757" timestamp="2450570" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="216" _type="ShellCommand" date="Wed Jul 09 19:46:42 EDT 2025" starttimestamp="1752102351757" timestamp="2450729" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="217" _type="ShellCommand" date="Wed Jul 09 19:46:42 EDT 2025" starttimestamp="1752102351757" timestamp="2450817" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="218" _type="ShellCommand" date="Wed Jul 09 19:46:45 EDT 2025" starttimestamp="1752102351757" timestamp="2453973" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="219" _type="ShellCommand" date="Wed Jul 09 19:46:49 EDT 2025" starttimestamp="1752102351757" timestamp="2457938" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="220" _type="ShellCommand" date="Wed Jul 09 19:46:49 EDT 2025" starttimestamp="1752102351757" timestamp="2458084" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="213" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:46:29 EDT 2025" docASTNodeCount="81" docActiveCodeLength="496" docExpressionCount="52" docLength="496" projectName="Assn2" starttimestamp="1752102351757" timestamp="2438115">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Avatar.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;import mp.shapes.Moveable;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.AVATAR)@StructurePattern(StructurePatternNames.BEAN_PATTERN)public interface Avatar extends Moveable, Scrollable {    ImageShape getHead();    StringShape getStringShape();    Angle getArms();    Angle getLegs();    void move(int dx, int dy);    void scale(double factor);}")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <DocumentChange __id="221" _type="Insert" date="Wed Jul 09 19:46:49 EDT 2025" docASTNodeCount="87" docActiveCodeLength="528" docExpressionCount="57" docLength="528" length="32" offset="20" repeat="2" starttimestamp="1752102351757" timestamp="2458136" timestamp2="2458136">
    <text><![CDATA[import javax.swing.Scrollable;

]]></text>
  </DocumentChange>
  <Command __id="223" _type="MoveCaretCommand" caretOffset="350" date="Wed Jul 09 19:46:54 EDT 2025" docOffset="525" starttimestamp="1752102351757" timestamp="2463069" />
  <Command __id="224" _type="MoveCaretCommand" caretOffset="350" date="Wed Jul 09 19:46:54 EDT 2025" docOffset="525" starttimestamp="1752102351757" timestamp="2463069" />
  <Command __id="225" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:46:55 EDT 2025" starttimestamp="1752102351757" timestamp="2463927" />
  <Command __id="226" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:46:55 EDT 2025" starttimestamp="1752102351757" timestamp="2464021" />
  <Command __id="227" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:46:55 EDT 2025" starttimestamp="1752102351757" timestamp="2464167" />
  <Command __id="229" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:47:02 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2470564" />
  <Command __id="230" _type="MoveCaretCommand" caretOffset="748" date="Wed Jul 09 19:47:07 EDT 2025" docOffset="952" starttimestamp="1752102351757" timestamp="2475903" />
  <Command __id="231" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:47:08 EDT 2025" starttimestamp="1752102351757" timestamp="2476619" />
  <Command __id="232" _type="CopyCommand" date="Wed Jul 09 19:47:09 EDT 2025" starttimestamp="1752102351757" timestamp="2477474" />
  <Command __id="233" _type="ShellCommand" date="Wed Jul 09 19:47:09 EDT 2025" starttimestamp="1752102351757" timestamp="2478144" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="234" _type="ShellCommand" date="Wed Jul 09 19:47:28 EDT 2025" starttimestamp="1752102351757" timestamp="2497048" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="228" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:47:02 EDT 2025" docASTNodeCount="428" docActiveCodeLength="2545" docExpressionCount="299" docLength="2598" projectName="Assn2" starttimestamp="1752102351757" timestamp="2470372">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\AvatarImpl.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
    @Override
    public void scroll(final String limb, final int dx, final int dy) {
        if (limb.equalsIgnoreCase("left arm")) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right arm")) {
            arms.getRightLine().setX(arms.getRightLine().getX() + dx);
            arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("left leg")) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right leg")) {
            legs.getRightLine().setX(legs.getRightLine().getX() + dx);
            legs.getRightLine().setY(legs.getRightLine().getY() + dy);
        }
    }

    public void scale(final double factor) {
        // Scale the head (if it has width/height properties)
        if (head instanceof ImageShape) {
            head.setWidth((int) (head.getWidth() * factor));
            head.setHeight((int) (head.getHeight() * factor));
        }
    }
}]]></snapshot>
  </Command>
  <Command __id="235" _type="MoveCaretCommand" caretOffset="667" date="Wed Jul 09 19:47:30 EDT 2025" docOffset="871" starttimestamp="1752102351757" timestamp="2499110" />
  <Command __id="236" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:47:31 EDT 2025" starttimestamp="1752102351757" timestamp="2499762" />
  <DocumentChange __id="237" _type="Delete" date="Wed Jul 09 19:47:32 EDT 2025" docASTNodeCount="1" docActiveCodeLength="0" docExpressionCount="0" docLength="0" endLine="76" length="2598" offset="0" startLine="0" starttimestamp="1752102351757" timestamp="2500807">
    <text><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }
    @Override
    public void scroll(final String limb, final int dx, final int dy) {
        if (limb.equalsIgnoreCase("left arm")) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right arm")) {
            arms.getRightLine().setX(arms.getRightLine().getX() + dx);
            arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("left leg")) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right leg")) {
            legs.getRightLine().setX(legs.getRightLine().getX() + dx);
            legs.getRightLine().setY(legs.getRightLine().getY() + dy);
        }
    }

    public void scale(final double factor) {
        // Scale the head (if it has width/height properties)
        if (head instanceof ImageShape) {
            head.setWidth((int) (head.getWidth() * factor));
            head.setHeight((int) (head.getHeight() * factor));
        }
    }
}]]></text>
  </DocumentChange>
  <Command __id="238" _type="EclipseCommand" commandID="org.eclipse.ui.edit.delete" date="Wed Jul 09 19:47:32 EDT 2025" starttimestamp="1752102351757" timestamp="2500815" />
  <Command __id="240" _type="PasteCommand" date="Wed Jul 09 19:47:34 EDT 2025" starttimestamp="1752102351757" timestamp="2502314" />
  <Command __id="241" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:47:35 EDT 2025" starttimestamp="1752102351757" timestamp="2504177" />
  <Command __id="242" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:47:36 EDT 2025" starttimestamp="1752102351757" timestamp="2504271" />
  <Command __id="243" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:47:36 EDT 2025" starttimestamp="1752102351757" timestamp="2504340" />
  <Command __id="244" _type="ShellCommand" date="Wed Jul 09 19:47:54 EDT 2025" starttimestamp="1752102351757" timestamp="2522612" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="245" _type="ShellCommand" date="Wed Jul 09 19:47:54 EDT 2025" starttimestamp="1752102351757" timestamp="2522759" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="270" _type="ShellCommand" date="Wed Jul 09 19:48:10 EDT 2025" starttimestamp="1752102351757" timestamp="2539048" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="271" _type="ShellCommand" date="Wed Jul 09 19:48:10 EDT 2025" starttimestamp="1752102351757" timestamp="2539193" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="281" _type="SelectTextCommand" caretOffset="375" date="Wed Jul 09 19:48:11 EDT 2025" end="375" start="371" starttimestamp="1752102351757" timestamp="2539380" />
  <Command __id="282" _type="MoveCaretCommand" caretOffset="393" date="Wed Jul 09 19:48:19 EDT 2025" docOffset="568" starttimestamp="1752102351757" timestamp="2548164" />
  <Command __id="283" _type="MoveCaretCommand" caretOffset="393" date="Wed Jul 09 19:48:19 EDT 2025" docOffset="568" starttimestamp="1752102351757" timestamp="2548165" />
  <DocumentChange __id="239" _type="Insert" date="Wed Jul 09 19:47:34 EDT 2025" docASTNodeCount="500" docActiveCodeLength="3254" docExpressionCount="352" docLength="3324" length="3324" offset="0" starttimestamp="1752102351757" timestamp="2502299">
    <text><![CDATA[package mp.bridge;

import java.beans.PropertyChangeListener;
import java.util.List;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Moveable;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable, PropertyListenerRegisterer {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }

    private void layoutAtOrigin() {}

    @Override
    public ImageShape getHead() { return head; }

    @Override
    public StringShape getStringShape() { return speech; }

    @Override
    public Angle getArms() { return arms; }

    @Override
    public Angle getLegs() { return legs; }

    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }

    @Override
    public void scroll(final String limb, final int dx, final int dy) {
        if (limb.equalsIgnoreCase("left arm")) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right arm")) {
            arms.getRightLine().setX(arms.getRightLine().getX() + dx);
            arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("left leg")) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right leg")) {
            legs.getRightLine().setX(legs.getRightLine().getX() + dx);
            legs.getRightLine().setY(legs.getRightLine().getY() + dy);
        }
    }

    public void scale(final double factor) {
        head.setWidth((int) (head.getWidth() * factor));
        head.setHeight((int) (head.getHeight() * factor));
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        head.addPropertyChangeListener(listener);
        speech.addPropertyChangeListener(listener);
        // if arms/legs support property listening:
        arms.getLeftLine().addPropertyChangeListener(listener);
        arms.getRightLine().addPropertyChangeListener(listener);
        legs.getLeftLine().addPropertyChangeListener(listener);
        legs.getRightLine().addPropertyChangeListener(listener);
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return head.getPropertyChangeListeners(); // or merged list if needed
    }
}
]]></text>
  </DocumentChange>
  <DocumentChange __id="246" _type="Insert" date="Wed Jul 09 19:47:54 EDT 2025" docASTNodeCount="566" docActiveCodeLength="3802" docExpressionCount="381" docLength="4042" length="718" offset="3319" repeat="20" starttimestamp="1752102351757" timestamp="2522900" timestamp2="2522958">
    <text><![CDATA[

	@Override
	public Dimension getPreferredScrollableViewportSize() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int getScrollableUnitIncrement(Rectangle visibleRect, int orientation, int direction) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int getScrollableBlockIncrement(Rectangle visibleRect, int orientation, int direction) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public boolean getScrollableTracksViewportWidth() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean getScrollableTracksViewportHeight() {
		// TODO Auto-generated method stub
		return false;
	}]]></text>
  </DocumentChange>
  <DocumentChange __id="266" _type="Insert" date="Wed Jul 09 19:47:54 EDT 2025" docASTNodeCount="578" docActiveCodeLength="3858" docExpressionCount="391" docLength="4098" length="56" offset="22" repeat="4" starttimestamp="1752102351757" timestamp="2522960" timestamp2="2522965">
    <text><![CDATA[import java.awt.Dimension;
import java.awt.Rectangle;
]]></text>
  </DocumentChange>
  <Command __id="272" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:48:11 EDT 2025" docASTNodeCount="87" docActiveCodeLength="528" docExpressionCount="57" docLength="528" projectName="Assn2" starttimestamp="1752102351757" timestamp="2539253">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Avatar.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;"), Diff(INSERT,"import javax.swing.Scrollable;"), Diff(EQUAL,"import mp.shapes.Moveable;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.AVATAR)@StructurePattern(StructurePatternNames.BEAN_PATTERN)public interface Avatar extends Moveable, Scrollable {    ImageShape getHead();    StringShape getStringShape();    Angle getArms();    Angle getLegs();    void move(int dx, int dy);    void scale(double factor);}")]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import javax.swing.Scrollable;

import mp.shapes.Moveable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable, Scrollable {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
    void scale(double factor);
}
]]></snapshot>
  </Command>
  <DocumentChange __id="273" _type="Insert" date="Wed Jul 09 19:48:11 EDT 2025" docASTNodeCount="100" docActiveCodeLength="571" docExpressionCount="62" docLength="571" length="43" offset="525" repeat="8" starttimestamp="1752102351757" timestamp="2539284" timestamp2="2539293">
    <text><![CDATA[
	void scroll(String limb, int dx, int dy);]]></text>
  </DocumentChange>
  <Command __id="284" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:48:21 EDT 2025" starttimestamp="1752102351757" timestamp="2549327" />
  <Command __id="285" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:48:21 EDT 2025" starttimestamp="1752102351757" timestamp="2549420" />
  <Command __id="286" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:48:21 EDT 2025" starttimestamp="1752102351757" timestamp="2549506" />
  <Command __id="288" _type="MoveCaretCommand" caretOffset="4098" date="Wed Jul 09 19:48:28 EDT 2025" docOffset="4098" starttimestamp="1752102351757" timestamp="2556270" />
  <Command __id="289" _type="MoveCaretCommand" caretOffset="3685" date="Wed Jul 09 19:48:38 EDT 2025" docOffset="3685" starttimestamp="1752102351757" timestamp="2566893" />
  <Command __id="290" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:48:39 EDT 2025" starttimestamp="1752102351757" timestamp="2567642" />
  <Command __id="291" _type="CopyCommand" date="Wed Jul 09 19:48:40 EDT 2025" starttimestamp="1752102351757" timestamp="2568445" />
  <Command __id="292" _type="ShellCommand" date="Wed Jul 09 19:48:40 EDT 2025" starttimestamp="1752102351757" timestamp="2568926" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="293" _type="ShellCommand" date="Wed Jul 09 19:48:47 EDT 2025" starttimestamp="1752102351757" timestamp="2576064" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="294" _type="MoveCaretCommand" caretOffset="3505" date="Wed Jul 09 19:48:48 EDT 2025" docOffset="3505" starttimestamp="1752102351757" timestamp="2577181" />
  <Command __id="295" _type="ShellCommand" date="Wed Jul 09 19:48:57 EDT 2025" starttimestamp="1752102351757" timestamp="2585750" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="296" _type="ShellCommand" date="Wed Jul 09 19:48:57 EDT 2025" starttimestamp="1752102351757" timestamp="2585899" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="304" _type="SelectTextCommand" caretOffset="464" date="Wed Jul 09 19:48:57 EDT 2025" end="464" start="404" starttimestamp="1752102351757" timestamp="2586065" />
  <Command __id="287" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:48:27 EDT 2025" docASTNodeCount="578" docActiveCodeLength="3858" docExpressionCount="391" docLength="4098" projectName="Assn2" starttimestamp="1752102351757" timestamp="2556176">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\AvatarImpl.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;"), Diff(DELETE,""), Diff(INSERT,"

import java.awt.Dimension;
import java.awt.Rectangle;
import java.beans.PropertyChangeListener;
import java.util.List;
"), Diff(EQUAL,"import util.annotations.Tags;"), Diff(INSERT,"
"), Diff(EQUAL,"import util.annotations.StructurePattern;"), Diff(INSERT,"
"), Diff(EQUAL,"import util.annotations.StructurePatternNames;"), Diff(INSERT,"
"), Diff(EQUAL,"import main.StaticFactoryClass;"), Diff(INSERT,"
"), Diff(EQUAL,"import mp.shapes."), Diff(DELETE,"Locat"), Diff(INSERT,"Move"), Diff(EQUAL,"able;"), Diff(INSERT,"
"), Diff(EQUAL,"import "), Diff(DELETE,"mp.shapes.Moveable;"), Diff(INSERT,"util.models.PropertyListenerRegisterer;
"), Diff(EQUAL,"import tags301.Comp301Tags;"), Diff(INSERT,"

"), Diff(EQUAL,"@Tags(Comp301Tags.AVATAR)"), Diff(INSERT,"
"), Diff(EQUAL,"@StructurePattern(StructurePatternNames.BEAN_PATTERN)"), Diff(INSERT,"
"), Diff(EQUAL,"public class AvatarImpl implements Avatar, Moveable"), Diff(DELETE,"{"), Diff(INSERT,", PropertyListenerRegisterer {
"), Diff(EQUAL,"    private final ImageShape head;"), Diff(INSERT,"
"), Diff(EQUAL,"    private final StringShape speech;"), Diff(INSERT,"
"), Diff(EQUAL,"    private final Angle arms;"), Diff(INSERT,"
"), Diff(EQUAL,"    private final Angle legs;"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    public AvatarImpl(final ImageShape h) {"), Diff(INSERT,"
"), Diff(EQUAL,"        this.head = h;"), Diff(INSERT,"
"), Diff(EQUAL,"        this.speech = new SpeechBubble(); "), Diff(INSERT,"
"), Diff(EQUAL,"        this.arms = StaticFactoryClass.legsFactoryMethod();"), Diff(INSERT,"
"), Diff(EQUAL,"        this.legs = StaticFactoryClass.legsFactoryMethod();"), Diff(INSERT,"
"), Diff(EQUAL,"        layoutAtOrigin();"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(INSERT,"

"), Diff(EQUAL,"    private void layoutAtOrigin()"), Diff(DELETE,"{    }"), Diff(INSERT," {}

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public ImageShape getHead()"), Diff(INSERT," "), Diff(EQUAL,"{ "), Diff(DELETE,"    	"), Diff(EQUAL,"return head; "), Diff(DELETE,"    }"), Diff(INSERT,"}

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public StringShape getStringShape() {"), Diff(DELETE,"       "), Diff(EQUAL," return speech;"), Diff(DELETE,"    }"), Diff(INSERT," }

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public Angle getArms()"), Diff(DELETE,"{    	"), Diff(INSERT," { "), Diff(EQUAL,"return arms; "), Diff(DELETE,"    }"), Diff(INSERT,"}

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public Angle getLegs()"), Diff(INSERT," "), Diff(EQUAL,"{ "), Diff(DELETE,"    	"), Diff(EQUAL,"return legs; "), Diff(DELETE,"    }"), Diff(INSERT,"}

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void move(final int dx, final int dy) {"), Diff(INSERT,"
"), Diff(EQUAL,"        head.setX(head.getX() + dx);"), Diff(INSERT,"
"), Diff(EQUAL,"        head.setY(head.getY() + dy);  "), Diff(INSERT,"
"), Diff(EQUAL,"        arms.move(dx, dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        legs.move(dx, dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        speech.setX(speech.getX() + dx);"), Diff(INSERT,"
"), Diff(EQUAL,"        speech.setY(speech.getY() + dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        layoutAtOrigin();"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(INSERT,"

"), Diff(EQUAL,"    @Override"), Diff(INSERT,"
"), Diff(EQUAL,"    public void scroll(final String limb, final int dx, final int dy) {"), Diff(INSERT,"
"), Diff(EQUAL,"        if (limb.equalsIgnoreCase("left arm")) {"), Diff(INSERT,"
"), Diff(EQUAL,"            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);"), Diff(INSERT,"
"), Diff(EQUAL,"            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        } else if (limb.equalsIgnoreCase("right arm")) {"), Diff(INSERT,"
"), Diff(EQUAL,"            arms.getRightLine().setX(arms.getRightLine().getX() + dx);"), Diff(INSERT,"
"), Diff(EQUAL,"            arms.getRightLine().setY(arms.getRightLine().getY() + dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        } else if (limb.equalsIgnoreCase("left leg")) {"), Diff(INSERT,"
"), Diff(EQUAL,"            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);"), Diff(INSERT,"
"), Diff(EQUAL,"            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        } else if (limb.equalsIgnoreCase("right leg")) {"), Diff(INSERT,"
"), Diff(EQUAL,"            legs.getRightLine().setX(legs.getRightLine().getX() + dx);"), Diff(INSERT,"
"), Diff(EQUAL,"            legs.getRightLine().setY(legs.getRightLine().getY() + dy);"), Diff(INSERT,"
"), Diff(EQUAL,"        }"), Diff(INSERT,"
"), Diff(EQUAL,"    }"), Diff(DELETE,""), Diff(INSERT,"

"), Diff(EQUAL,"    public void scale(final double factor) {"), Diff(INSERT,"
"), Diff(EQUAL,"        "), Diff(DELETE,"// Scale the head (if it has width/height properties)        if (head instanceof ImageShape) {            head.setWidth((int) (head.getWidth() * factor));            head.setHeight((int) (head.getHeight() * factor));        }    }}"), Diff(INSERT,"head.setWidth((int) (head.getWidth() * factor));
        head.setHeight((int) (head.getHeight() * factor));
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        head.addPropertyChangeListener(listener);
        speech.addPropertyChangeListener(listener);
        // if arms/legs support property listening:
        arms.getLeftLine().addPropertyChangeListener(listener);
        arms.getRightLine().addPropertyChangeListener(listener);
        legs.getLeftLine().addPropertyChangeListener(listener);
        legs.getRightLine().addPropertyChangeListener(listener);
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return head.getPropertyChangeListeners(); // or merged list if needed
    }

	@Override
	public Dimension getPreferredScrollableViewportSize() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int getScrollableUnitIncrement(Rectangle visibleRect, int orientation, int direction) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int getScrollableBlockIncrement(Rectangle visibleRect, int orientation, int direction) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public boolean getScrollableTracksViewportWidth() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean getScrollableTracksViewportHeight() {
		// TODO Auto-generated method stub
		return false;
	}
}
")]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import java.awt.Dimension;
import java.awt.Rectangle;
import java.beans.PropertyChangeListener;
import java.util.List;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Moveable;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar, Moveable, PropertyListenerRegisterer {
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble(); 
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }

    private void layoutAtOrigin() {}

    @Override
    public ImageShape getHead() { return head; }

    @Override
    public StringShape getStringShape() { return speech; }

    @Override
    public Angle getArms() { return arms; }

    @Override
    public Angle getLegs() { return legs; }

    @Override
    public void move(final int dx, final int dy) {
        head.setX(head.getX() + dx);
        head.setY(head.getY() + dy);  
        arms.move(dx, dy);
        legs.move(dx, dy);
        speech.setX(speech.getX() + dx);
        speech.setY(speech.getY() + dy);
        layoutAtOrigin();
    }

    @Override
    public void scroll(final String limb, final int dx, final int dy) {
        if (limb.equalsIgnoreCase("left arm")) {
            arms.getLeftLine().setX(arms.getLeftLine().getX() + dx);
            arms.getLeftLine().setY(arms.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right arm")) {
            arms.getRightLine().setX(arms.getRightLine().getX() + dx);
            arms.getRightLine().setY(arms.getRightLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("left leg")) {
            legs.getLeftLine().setX(legs.getLeftLine().getX() + dx);
            legs.getLeftLine().setY(legs.getLeftLine().getY() + dy);
        } else if (limb.equalsIgnoreCase("right leg")) {
            legs.getRightLine().setX(legs.getRightLine().getX() + dx);
            legs.getRightLine().setY(legs.getRightLine().getY() + dy);
        }
    }

    public void scale(final double factor) {
        head.setWidth((int) (head.getWidth() * factor));
        head.setHeight((int) (head.getHeight() * factor));
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        head.addPropertyChangeListener(listener);
        speech.addPropertyChangeListener(listener);
        // if arms/legs support property listening:
        arms.getLeftLine().addPropertyChangeListener(listener);
        arms.getRightLine().addPropertyChangeListener(listener);
        legs.getLeftLine().addPropertyChangeListener(listener);
        legs.getRightLine().addPropertyChangeListener(listener);
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return head.getPropertyChangeListeners(); // or merged list if needed
    }

	@Override
	public Dimension getPreferredScrollableViewportSize() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int getScrollableUnitIncrement(Rectangle visibleRect, int orientation, int direction) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int getScrollableBlockIncrement(Rectangle visibleRect, int orientation, int direction) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public boolean getScrollableTracksViewportWidth() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean getScrollableTracksViewportHeight() {
		// TODO Auto-generated method stub
		return false;
	}
}
]]></snapshot>
  </Command>
  <Command __id="297" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:48:57 EDT 2025" docASTNodeCount="100" docActiveCodeLength="571" docExpressionCount="62" docLength="571" projectName="Assn2" starttimestamp="1752102351757" timestamp="2585969">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\Avatar.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;import javax.swing.Scrollable;import mp.shapes.Moveable;import tags301.Comp301Tags;import util.annotations.StructurePattern;import util.annotations.StructurePatternNames;import util.annotations.Tags;@Tags(Comp301Tags.AVATAR)@StructurePattern(StructurePatternNames.BEAN_PATTERN)public interface Avatar extends Moveable, Scrollable {    ImageShape getHead();    StringShape getStringShape();    Angle getArms();    Angle getLegs();    void move(int dx, int dy);    void scale(double factor);"), Diff(INSERT,"	void scroll(String limb, int dx, int dy);"), Diff(EQUAL,"}")]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import javax.swing.Scrollable;

import mp.shapes.Moveable;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable, Scrollable {
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int dx, int dy);
    void scale(double factor);
	void scroll(String limb, int dx, int dy);
}
]]></snapshot>
  </Command>
  <DocumentChange __id="298" _type="Insert" date="Wed Jul 09 19:48:57 EDT 2025" docASTNodeCount="107" docActiveCodeLength="631" docExpressionCount="65" docLength="631" length="60" offset="568" repeat="2" starttimestamp="1752102351757" timestamp="2585984" timestamp2="2585984">
    <text><![CDATA[
	List<PropertyChangeListener> getPropertyChangeListeners();]]></text>
  </DocumentChange>
  <DocumentChange __id="300" _type="Insert" date="Wed Jul 09 19:48:57 EDT 2025" docASTNodeCount="119" docActiveCodeLength="697" docExpressionCount="75" docLength="697" length="66" offset="20" repeat="4" starttimestamp="1752102351757" timestamp="2585985" timestamp2="2585987">
    <text><![CDATA[import java.beans.PropertyChangeListener;
import java.util.List;

]]></text>
  </DocumentChange>
  <Command __id="305" _type="MoveCaretCommand" caretOffset="467" date="Wed Jul 09 19:48:59 EDT 2025" docOffset="697" starttimestamp="1752102351757" timestamp="2588052" />
  <Command __id="306" _type="MoveCaretCommand" caretOffset="467" date="Wed Jul 09 19:48:59 EDT 2025" docOffset="697" starttimestamp="1752102351757" timestamp="2588052" />
  <Command __id="308" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:49:05 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2593699" />
  <Command __id="309" _type="ShellCommand" date="Wed Jul 09 19:49:20 EDT 2025" starttimestamp="1752102351757" timestamp="2608618" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="310" _type="ShellCommand" date="Wed Jul 09 19:49:20 EDT 2025" starttimestamp="1752102351757" timestamp="2608771" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="313" _type="MoveCaretCommand" caretOffset="665" date="Wed Jul 09 19:49:22 EDT 2025" docOffset="881" starttimestamp="1752102351757" timestamp="2611196" />
  <Command __id="314" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:49:24 EDT 2025" starttimestamp="1752102351757" timestamp="2612889" />
  <Command __id="315" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:49:24 EDT 2025" starttimestamp="1752102351757" timestamp="2612983" />
  <Command __id="316" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:49:24 EDT 2025" starttimestamp="1752102351757" timestamp="2613070" />
  <Command __id="317" _type="MoveCaretCommand" caretOffset="665" date="Wed Jul 09 19:49:35 EDT 2025" docOffset="881" starttimestamp="1752102351757" timestamp="2624125" />
  <Command __id="318" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:49:36 EDT 2025" starttimestamp="1752102351757" timestamp="2624895" />
  <Command __id="319" _type="CopyCommand" date="Wed Jul 09 19:49:37 EDT 2025" starttimestamp="1752102351757" timestamp="2625674" />
  <Command __id="320" _type="ShellCommand" date="Wed Jul 09 19:49:37 EDT 2025" starttimestamp="1752102351757" timestamp="2626202" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="321" _type="ShellCommand" date="Wed Jul 09 19:49:46 EDT 2025" starttimestamp="1752102351757" timestamp="2634481" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="322" _type="MoveCaretCommand" caretOffset="665" date="Wed Jul 09 19:49:47 EDT 2025" docOffset="881" starttimestamp="1752102351757" timestamp="2635990" />
  <Command __id="323" _type="EclipseCommand" commandID="org.eclipse.ui.edit.selectAll" date="Wed Jul 09 19:49:48 EDT 2025" starttimestamp="1752102351757" timestamp="2636638" />
  <Command __id="325" _type="EclipseCommand" commandID="org.eclipse.ui.edit.delete" date="Wed Jul 09 19:49:49 EDT 2025" starttimestamp="1752102351757" timestamp="2637609" />
  <Command __id="327" _type="PasteCommand" date="Wed Jul 09 19:49:50 EDT 2025" starttimestamp="1752102351757" timestamp="2638560" />
  <Command __id="307" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:49:05 EDT 2025" docASTNodeCount="129" docActiveCodeLength="871" docExpressionCount="74" docLength="871" projectName="Assn2" starttimestamp="1752102351757" timestamp="2593490">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\BridgeScene.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import mp.bridge.Scrollable;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
}]]></snapshot>
  </Command>
  <DocumentChange __id="311" _type="Delete" date="Wed Jul 09 19:49:20 EDT 2025" docASTNodeCount="127" docActiveCodeLength="861" docExpressionCount="73" docLength="861" endLine="12" length="10" offset="390" startLine="12" starttimestamp="1752102351757" timestamp="2608794">
    <text><![CDATA[Scrollable]]></text>
  </DocumentChange>
  <DocumentChange __id="312" _type="Insert" date="Wed Jul 09 19:49:20 EDT 2025" docASTNodeCount="133" docActiveCodeLength="883" docExpressionCount="78" docLength="883" length="22" offset="390" starttimestamp="1752102351757" timestamp="2608797">
    <text><![CDATA[javax.swing.Scrollable]]></text>
  </DocumentChange>
  <DocumentChange __id="324" _type="Delete" date="Wed Jul 09 19:49:49 EDT 2025" docASTNodeCount="1" docActiveCodeLength="0" docExpressionCount="0" docLength="0" endLine="27" length="883" offset="0" startLine="0" starttimestamp="1752102351757" timestamp="2637602">
    <text><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import mp.bridge.Scrollable;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends javax.swing.Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String s);
    public AScalableRectangleInterface getKnightArea();
    public AScalableRectangleInterface getGuardArea();
    public Gorge getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
}]]></text>
  </DocumentChange>
  <DocumentChange __id="326" _type="Insert" date="Wed Jul 09 19:49:50 EDT 2025" docASTNodeCount="127" docActiveCodeLength="853" docExpressionCount="76" docLength="853" length="853" offset="0" starttimestamp="1752102351757" timestamp="2638547">
    <text><![CDATA[package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends javax.swing.Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    void passed();
    void failed();
    void approach(final Avatar avatar);
    void say(final String s);
    AScalableRectangleInterface getKnightArea();
    AScalableRectangleInterface getGuardArea();
    Gorge getGorge();
    boolean getOccupied();
    boolean getKnightTurn();
    void scroll(int dx, int dy);
}
]]></text>
  </DocumentChange>
  <Command __id="328" _type="MoveCaretCommand" caretOffset="684" date="Wed Jul 09 19:49:52 EDT 2025" docOffset="684" starttimestamp="1752102351757" timestamp="2640758" />
  <Command __id="329" _type="EclipseCommand" commandID="org.eclipse.ui.file.save" date="Wed Jul 09 19:49:53 EDT 2025" starttimestamp="1752102351757" timestamp="2641497" />
  <Command __id="330" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:49:53 EDT 2025" starttimestamp="1752102351757" timestamp="2641640" />
  <Command __id="331" _type="EclipseCommand" commandID="" date="Wed Jul 09 19:49:53 EDT 2025" starttimestamp="1752102351757" timestamp="2641701" />
  <Command __id="332" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 19:50:00 EDT 2025" docASTNodeCount="454" docActiveCodeLength="3433" docExpressionCount="266" docLength="3433" projectName="Assn2" starttimestamp="1752102351757" timestamp="2648296">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\ArthurHead.java]]></filePath>
    <diff><![CDATA[Diff(EQUAL,"package mp.bridge;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;

@Tags(Comp301Tags.IMAGE_PATTERN)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead extends ImageShape {
    private String fileName = "images/arthur.jpg";
    private int x, y, width = 50, height = 50;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    public ArthurHead() {
    }

    @Override
    public String getImageFileName() {
        return fileName;
    }

    @Override
    public void setImageFileName(final String fileName) {
        String oldValue = this.fileName;
        this.fileName = fileName;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "imageFileName", oldValue, fileName);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(final int value) {
        int oldValue = this.x;
        this.x = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "x", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(final int value) {
        int oldValue = this.y;
        this.y = value;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "y", oldValue, value);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public void setWidth(final int width) {
        int oldValue = this.width;
        this.width = width;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "width", oldValue, width);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setHeight(final int height) {
        int oldValue = this.height;
        this.height = height;
        PropertyChangeEvent event = new PropertyChangeEvent(this, "height", oldValue, height);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }

    @Override
    public List<PropertyChangeListener> getPropertyChangeListeners() {
        return new ArrayList<>(propertyChangeListeners);
    }

    @Override
    public void addPropertyChangeListener(final PropertyChangeListener listener) {
        if (listener != null && !propertyChangeListeners.contains(listener)) {
            propertyChangeListeners.add(listener);
        }
    }

    @Override
    public void removePropertyChangeListener(final PropertyChangeListener listener) {
        propertyChangeListeners.remove(listener);
    }
}
")]]></diff>
    <snapshot><![CDATA[null]]></snapshot>
  </Command>
  <Command __id="333" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:50:00 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2648513" />
  <Command __id="335" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 19:50:06 EDT 2025" docOffset="0" starttimestamp="1752102351757" timestamp="2655022" />
  <Command __id="336" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655125">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,variables,Variables:[]]]></CSVRow>
  </Command>
  <Command __id="337" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655125">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,accessModifiersUsed,Access Modifiers Used: []]]></CSVRow>
  </Command>
  <Command __id="338" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655125">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,methods,NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScalableRectangleInterface, public  getGuardArea:->mp.shapes.AScalableRectangleInterface, public  getGorge:->mp.shapes.Gorge, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]]]></CSVRow>
  </Command>
  <Command __id="339" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655125">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,properties,Properties:[readonly  p-v:5 access:public KnightTurn:boolean(public , null), readonly  p-v:5 access:public Gorge:mp.shapes.Gorge(public , null), readonly  p-v:5 access:public Occupied:boolean(public , null), readonly  p-v:5 access:package Arthur:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public KnightArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:5 access:package Guard:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Lancelot:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public GuardArea:mp.shapes.AScalableRectangleInterface(public , null), readonly  p-v:5 access:package Galahad:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Robin:mp.bridge.Avatar(default , null)]]]></CSVRow>
  </Command>
  <Command __id="340" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655125">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,aggregateStatistics, Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:14 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:5 Public Methods Fraction:0.6428571428571429 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.35714285714285715 Private  Methods Fraction:0.0 Average Method Access:0.7142857142857143 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:10 Public Properties Fraction:0.5 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.5 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:1.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0]]></CSVRow>
  </Command>
  <Command __id="341" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655126">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,innerTypes,[]]]></CSVRow>
  </Command>
  <Command __id="342" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655126">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,superTypes,[]]]></CSVRow>
  </Command>
  <Command __id="343" _type="CheckStyleCommand" date="Wed Jul 09 19:50:06 EDT 2025" starttimestamp="1752102351757" timestamp="2655126">
    <CSVRow><![CDATA[14,Wed Jul 09 19:49:53 EDT 2025,true,mp\bridge\BridgeScene.java,tags,mp.bridge.BridgeScene,@Comp301Tags.BRIDGE_SCENE]]></CSVRow>
  </Command>

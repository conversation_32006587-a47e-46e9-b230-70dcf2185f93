package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScalableRectangleInterface;
import mp.shapes.Gorge;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene extends javax.swing.Scrollable {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    void passed();
    void failed();
    void approach(final Avatar avatar);
    void say(final String s);
    AScalableRectangleInterface getKnightArea();
    AScalableRectangleInterface getGuardArea();
    Gorge getGorge();
    boolean getOccupied();
    boolean getKnightTurn();
    void scroll(int dx, int dy);
}
